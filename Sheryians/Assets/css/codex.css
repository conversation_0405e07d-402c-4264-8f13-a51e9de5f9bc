* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
  font-family: "urbanians-regular", sans-serif;
}

html,
body {
  height: 100%;
  width: 100%;
}

@font-face {
  font-family: "bwGrad-thin";
  src: url("../../Assets/fonts/BwGradualDEMO-Thin.otf");
  font-weight: 100;
}
@font-face {
  font-family: "bwGrad-regular";
  src: url("../../Assets/fonts/BwGradualDEMO-Medium.otf");
  font-weight: 400;
}
@font-face {
  font-family: "bwGrad-medium";
  src: url("../../Assets/fonts/BwGradualDEMO-Regular.otf");
  font-weight: 400;
}
@font-face {
  font-family: "bwGrad-bold";
  src: url("../../Assets/fonts/BwGradualDEMO-Bold.otf");
  font-weight: 700;
}
@font-face {
  font-family: "bwGrad-bold";
  src: url("../../Assets/fonts/BwGradualDEMO-Bold.otf");
  font-weight: 700;
}
@font-face {
  font-family: "urbanians-thin";
  src: url("../../Assets/fonts/Urbanist-Thin.ttf");
  font-weight: 100;
}
@font-face {
  font-family: "urbanians-regular";
  src: url("../../Assets/fonts/Urbanist-Regular.ttf");
  font-weight: 400;
}
@font-face {
  font-family: "urbanians-medium";
  src: url("../../Assets/fonts/Urbanist-Medium.ttf");
  font-weight: 400;
}
@font-face {
  font-family: "urbanians-bold";
  src: url("../../Assets/fonts/Urbanist-Bold.ttf");
  font-weight: 700;
}
@font-face {
  font-family: "poppins-thin";
  src: url("../../Assets/fonts/Poppins-Thin.ttf");
  font-weight: 100;
}
@font-face {
  font-family: "poppins-regular";
  src: url("../../Assets/fonts/Poppins-Regular.ttf");
  font-weight: 400;
}
@font-face {
  font-family: "poppins-bold";
  src: url("../../Assets/fonts/Poppins-Bold.ttf");
  font-weight: 700;
}

.inner-shadow {
  box-shadow: inset 0.05px 0 10px rgba(255, 255, 255, 0.03);
}
.bg-grad {
  background: radial-gradient(#141414 70%, #1b1b1b);
}
.bg-grad-visible {
  background: radial-gradient(#14141400 50%, #1b1b1b);
}
.bg-grad-box {
  background-color: #2120205f;
  border-radius: 40px;
}

.inner-box {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

.box {
  transform: rotateX("0deg");
}

.HeroVideo {
  position: absolute;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
  filter: saturate(0);
  opacity: 40%;
}

.HeroVideoCover {
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000000;
  background: radial-gradient(rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 60%);
}

.page1 {
  height: 100vh;
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.heading {
  background: radial-gradient(
    46.73% 118.81% at 50% 38.57%,
    #ffffff 25%,
    #535353 100%
  );
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}

.bg-grad-hover:hover {
  background: radial-gradient(#1b1b1b 70%, #141414);
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

.page7 {
  height: 40vh;
  width: 100%;
  /* background-color: pink; */
  display: flex;
  justify-content: center;
  align-items: center;
}
.page7 .notforAll {
  width: 100%;
  /* border-radius: 4rem; */
  --gap: 30rem;
  display: flex;
  gap: var(--gap);
  overflow: hidden;
}

.page7 .notforAll .scroller {
  display: flex;
  /* gap: ; */
  flex-shrink: 0;
  animation: scroll 40s linear infinite;
}

.page7 .notforAll .content {
  font-family: "bwGrad-regular";
  font-size: 12.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

@keyframes scroll {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-50%);
  }
}

.page7 .notforAll .content .circle {
  height: 3.5rem;
  width: 3.5rem;
  margin: 0 1rem;
  border-radius: 50%;
  background-color: #e4e4e7;
}

.page7 .notforAll .content .imgeShit {
  height: 15rem;
  width: 15rem;
  position: relative;
  margin: 0 1rem 0 2rem;
}

.page7 .notforAll .content .imgeShit .img1 {
  height: 99%;
  width: 90%;
  border-radius: 1.3rem;
  background-image: url("../images/companies/harshbhaiyaout.png");
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  transform: rotate(-10deg);

  /* here’s the pink magic */
  filter: brightness(0.8) sepia(1) saturate(5) hue-rotate(300deg);
}

.page7 .notforAll .content .imgeShit .img2 {
  height: 99%;
  width: 90%;
  border-radius: 1.3rem;
  position: absolute;
  top: 0%;
  background-image: url("../images//companies/harshbhaiyaout.png");
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  z-index: 2;
}

/* ...................................................................................................................................................... */
/* ...................................................................................................................................................... */
/* ...................................................................................................................................................... */
/* ...................................................................................................................................................... */
/* ...................................................................................................................................................... */

.page8 {
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 11vh;
}
.page8 .cta {
  height: 85%;
  width: 90%;
  background-color: #e9e9e9;
  border-radius: 1rem;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding: 3.3rem 3rem;
}
.page8 .cta .stuff {
  height: 100%;
  width: 100%;
  display: flex;
}

.page8 .cta .stuff .left {
  height: 100%;
  width: 50%;
  /* background-color: pink; */
  color: black;
}
.page8 .right {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 1.5rem;
  overflow: hidden;
}

.wrapper {
  padding: 1.5rem 0;
  max-width: 42rem;
  width: 100%;
}

/* Form Container (Desktop/Tablet layout: row) */
.form-container {
  display: flex;
  flex-direction: row; /* Default for desktop/tablet */
  align-items: center;
  gap: 1rem; /* spacing between input and button */
}

/* Email Input Styling (Black background, White text) */
.email-input {
  width: 60%;
  padding: 0.7rem;
  font-size: 1.25rem; /* text-xl */
  background-color: #000; /* bg-black */
  color: #fff; /* text-white */
  border-radius: 1rem; /* rounded-full */
  border: 2px solid #000;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-2xl equivalent */
  transition: all 0.3s ease;
}
.email-input::placeholder {
  color: #9ca3af; /* placeholder-gray-500 */
}

/* Override default focus ring for the black input */
.email-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px #e9e9e9, 0 0 0 6px rgba(0, 0, 0, 1);
}

/* Subscribe Button Styling (White background, Black text) */
.subscribe-button {
  padding: 0.9rem 1.2rem;
  font-size: 1rem; /* text-xl */
  background-color: #ffffff; /* bg-white */
  color: #000; /* text-black */
  font-weight: 300; /* font-semibold */
  border-radius: 50px; /* rounded-full */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-xl */
  white-space: nowrap;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

/* Button interactions */
.subscribe-button:hover {
  background-color: #f3f4f6; /* hover:bg-gray-100 */
}
.subscribe-button:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.5); /* focus ring */
}

.info-text p {
  margin-top: 0.75rem;
  font-size: 1.3rem;
  color: black;
  font-weight: 500;
  margin-bottom: 0.7rem;
}
.page8 .cta .stuff .right {
  height: 100%;
  width: 55%;
  background-color: rgb(0, 0, 0);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.rightVDo {
  height: 90%;
  width: 90%;
}
.rightVDo video {
  height: 100%;
  width: 100%;
  object-fit: cover;
  /* display: none; */
}
@media (max-width: 950px) {
  .page8 .cta {
    padding: 1rem 1.5rem;
  }

  .page7 .notforAll .content {
    font-size: 5rem;
  }

  .page8 .cta .stuff {
    flex-direction: column;
    gap: 0.5rem;
  }
  .page8 .cta .stuff .left,
  .page8 .cta .stuff .right {
    width: 100%;
  }
  .page8 .cta .stuff .right {
    padding: 1rem;
  }

  .wrapper {
    max-width: 100%;
    padding: 1rem 0;
  }

  .form-container {
    flex-direction: column;
    align-items: stretch;
    gap: 0.7rem;
  }

  .email-input {
    width: 100%;
    font-size: 1rem;
    padding: 0.6rem;
  }

  .subscribe-button {
    width: 100%;
    font-size: 1rem;
    padding: 0.8rem 1rem;
  }
}
footer {
  background: radial-gradient(
      at bottom,
      rgba(21, 21, 21, 0.673),
      rgb(0, 0, 0) 65%
    ),
    url("../images//companies/BG.png");
  width: 100%;
  border-top: 1px solid rgb(50, 52, 53);
  border-radius: 2rem 2rem;
  background-size: cover;
  background-position: center;
  position: relative;
  background-repeat: no-repeat;
}

.sceneWrapper {
  height: 300vh;
  position: relative;
}

.scene {
  position: sticky;
  top: 0;
  background: url("../../Assets/images/master-grid.png");
  height: 100vh;
  width: 100%;
  perspective: 1000px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel {
  width: 100%;
  height: 120px;
  position: absolute;
  transform-style: preserve-3d;
  transform: translateZ(-288px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel__cell {
  position: absolute;
  height: 120px;
  width: 100%;
  left: 10px;
  top: 10px;
  line-height: 116px;
  font-size: 6rem;
  font-weight: bold;
  color: white;
  text-align: center;
  transition: transform 1s, opacity 1s;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.carousel_item {
  width: fit-content;
  margin: auto;
  position: relative;
}

.footer-content {
  position: relative;
  z-index: 10;
  padding: 40px;
  padding-top: 10vh; /* Pushing content down from the top edge for the logo */
  width: 100%;
  box-sizing: border-box; /* Include padding in width calculation */
}

.content-grid {
  display: flex;
  gap: 7rem;
}

/* Individual link/text styling */
.footer-column a,
.footer-column p {
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;
  display: block;
  color: #d1d5db; /* Default gray-300 */
  transition: color 200ms ease;
  text-decoration: none; /* Remove underline from links */
}

.footer-column a:hover {
  color: #ffffff; /* Hover to white */
}

.footer-column .highlight {
  color: #ffffff;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.footer-column .detail.mt {
  margin-top: 1rem;
}

.page9 p,
.page9 a {
  font-size: 1.2rem;
  font-weight: 500;
}

.companies {
  display: flex;
  align-items: center;
}
.companies img {
  flex-shrink: 0;
  width: 20rem;
  padding: 1rem 3rem;
  animation: translate 10s linear infinite;
}
@keyframes translate {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-500%);
  }
}

@media (max-width: 768px) {
  footer {
    height: 120vh;
  }
  .content-grid {
    flex-direction: column;
    gap: 2rem;
  }
}

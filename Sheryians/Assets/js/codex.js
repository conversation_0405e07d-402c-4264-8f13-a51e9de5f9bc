gsap.registerPlugin(ScrollTrigger);

// counter animation for metrics
gsap.to(".metrics h1", {
  scrollTrigger: {
    trigger: ".metrics",
    start: "top 80%",
    end: "bottom 20%",
  },
  onStart: () => {
    document.querySelectorAll(".metrics h1").forEach(() => {
      document.querySelectorAll(".metrics h1").forEach((el) => {
        let target = +el.getAttribute("data-value");
        let count = 0;
        let interval = setInterval(() => {
          el.innerText = count > target ? target + "k+" : count + "k+";
          if (count < target) {
            count +=
              target > 500
                ? Math.floor(Math.random() * 15)
                : Math.floor(Math.random() * 5);
          } else {
            clearInterval(interval);
          }
        }, 25); // Example interval time
      });
    });
  },
});

// company cards animation
document.querySelectorAll(".feature").forEach((feature) => {
  gsap.to(feature, {
    scrollTrigger: {
      trigger: feature,
      start: "top 5%",
      end: "bottom -50%",
      scrub: true,
    },
    scale: 0.5,
    opacity: 0,
    duration: 1,
    rotate: 20,
    ease: "power1.out",
  });
});

let videoUpdated = false;

// filler Animation
gsap.to("#filler", {
  scrollTrigger: {
    trigger: ".page4Content",
    start: "top 20%",
    end: "bottom 100%",
    scrub: true,
    onUpdate: (self) => {
      const progress = self.progress.toFixed(2); // progress is from 0 → 1
      const video = document.querySelector(".leftVideo video");

      // When we cross 0.5 (halfway) and it's not updated yet
      if (progress >= 0.5 && !videoUpdated) {
        video.src = "../Assets//images/companies/CtaVdo.mp4";
        videoUpdated = true;
        gsap.to(video, { scale: 0.8 });
      }

      // When we scroll back above halfway and it was updated before
      if (progress < 0.5 && videoUpdated) {
        video.src =
          "../Assets/images/companies/9cccea11555caca1079e3c3eacea2b13_720w.mp4";
        videoUpdated = false;
        gsap.to(video, { scale: 1 });
      }
    },
  },
  height: "100%",
  ease: "power1.out",
});

// grow and shrink animation
gsap
  .timeline({
    scrollTrigger: {
      trigger: "#whereEvolve",
      start: "top 80%",
      end: "bottom 10%",
      scrub: 1, // smooth scrubbing
      // markers: true, // Uncomment to debug
    },
  })
  // Grow
  .to("#whereEvolve", {
    width: "100%",
    ease: "power2.out",
  })
  // Shrink back
  .to("#whereEvolve", {
    width: "90%",
    duration: 1,
    ease: "power2.in",
  });

// rotate animation

const headings = gsap.utils.toArray(".box");
headings.forEach((heading, index) => {
  if (index != 0) heading.style.transform = `rotateX(${-index * 10}deg)`; // Initial state
});

const carousel = document.querySelector(".carousel");
const cells = document.querySelectorAll(".carousel__cell");

if (window.innerWidth > 768) {
  const cellCount = cells.length;
  const arc = window.innerWidth < 768 ? 20 : 40; // degrees between items
  const cellHeight = carousel.offsetHeight;
  const radius = Math.round(cellHeight / 2 / Math.sin((arc * Math.PI) / 360));

  // Position each cell around the X-axis (vertical 3D rotation)
  cells.forEach((cell, i) => {
    const cellAngle = i * arc;
    cell.style.transform = `rotateX(${-cellAngle}deg) translateZ(${radius}px)`;
    cell.style.opacity = i === 0 ? 1 : 0.3;
  });

  // Create a GSAP timeline that rotates the carousel
  const tl = gsap.timeline({
    scrollTrigger: {
      trigger: ".sceneWrapper",
      start: "top top",
      end: "bottom bottom",
      scrub: true,
    },
  });

  // Animate rotation step by step
  tl.to(carousel, {
    rotateX: `+=${(cellCount - 1) * arc}`,
    ease: "none",
    onUpdate: () => {
      const rotation = gsap.getProperty(carousel, "rotateX");
      const index = Math.round(rotation / arc) % cellCount;
      cells.forEach((cell, i) => {
        cell.style.opacity =
          i === index ? 1 : Math.abs(i - index) === 1 ? 0.3 : 0;
        cell.querySelectorAll(".inner-box").forEach((el) => {
          gsap.to(el, { opacity: 0, delay: 0.5 });
        });
        document
          .querySelectorAll(`#carousel${index + 1} .inner-box`)
          .forEach((el) => {
            gsap.to(el, { opacity: 1, delay: 0.5 });
          });
      });
    },
  });
} else {
  const tl = gsap.timeline({
    scrollTrigger: {
      trigger: ".sceneWrapper",
      start: "top top",
      end: "bottom bottom",
      scrub: true,
    },
  });
  const headingssssss = document.querySelectorAll(".carousel h1");
  headingssssss.forEach((heading, i) => {
    tl.from(heading, { opacity: 0, y: 50, duration: 1, stagger: 0.2 });
    if (i < headingssssss.length - 1) {
      tl.to(heading, { opacity: 0, y: -50, duration: 1, stagger: 0.2 });
    }
  });
}

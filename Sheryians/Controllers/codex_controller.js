const codexRegistration = require("../Models/codexRegistration");
const codexRankingModel = require("../Models/codexLeaderBoard");
const codexLeaderBoard = require("../Models/codexLeaderBoard");
const fs = require("fs");
const path = require("path");
const codexMcq = require("../Models/codexMcq");

module.exports.getCodex = (req, res) => {
  res.render("codexForm");
};

module.exports.codexRegister = async (req, res) => {
  const {
    fullName,
    email,
    phone,
    dob,
    experience,
    degree,
    call_status,
    status,
    paymentStatus,
  } = req.body;

  // Basic validation
  if (!fullName || typeof fullName !== "string" || fullName.trim().length < 2) {
    return res.status(400).send("Invalid or missing name");
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email || typeof email !== "string" || !emailRegex.test(email)) {
    return res.status(400).send("Invalid or missing email");
  }
  const phoneRegex = /^\d{10}$/;
  if (!phone || typeof phone !== "string" || !phoneRegex.test(phone)) {
    return res.status(400).send("Invalid or missing contact number");
  }
  if (!dob || isNaN(Date.parse(dob)) || new Date(dob) > new Date()) {
    return res.status(400).send("Invalid or missing date of birth");
  }
  if (
    !experience ||
    typeof experience !== "string" ||
    experience.trim().length < 2
  ) {
    return res.status(400).send("Invalid or missing experience");
  }
  if (!degree || typeof degree !== "string" || degree.trim().length < 2) {
    return res.status(400).send("Invalid or missing degree");
  }

  const isExisting = await codexRegistration.findOne({
    $or: [{ email }, { contact: phone }],
  });
  if (isExisting) {
    return res.status(203).redirect(`/kodex/ranking?id=${isExisting._id}`);
  }

  await codexRegistration.create(
    {
      name: fullName,
      email: email,
      contact: phone,
      dob: dob,
      experience: experience,
      degree: degree,
      call_status: call_status,
      status: status,
      paymentStatus: paymentStatus,
    },
    async (err, newRegistration) => {
      if (err) {
        console.log("Error in creating codex registration:", err);
        return res.status(500).json("Internal Server Error");
      }

      // Ensure codexRanking document exists, then push the user
      let rankingDoc = await codexRankingModel.findOne({
        name: "codexRanking",
      });

      console.log(newRegistration);
      if (!rankingDoc) {
        rankingDoc = await codexRankingModel.create({
          name: "codexRanking",
          ranking: [{ username: fullName, userId: newRegistration._id }],
        });
      } else {
        rankingDoc.ranking.push({
          username: fullName,
          userId: newRegistration._id,
        });
        await rankingDoc.save();
      }
      res.status(201).json({ id: newRegistration._id });
    }
  );
};

module.exports.codexRanking = async (req, res) => {
  const registrations = await codexLeaderBoard
    .findOne({ name: "codexRanking" })
    .exec();

  let myRankings = registrations?.ranking.find(
    (entry) => entry?.userId?.toString() === req.query.id
  );

  let myRank = registrations?.ranking.findIndex(
    (entry) => entry?.userId?.toString() === req.query.id
  );
  if (myRank !== -1) {
    myRank += 1; // Convert to 1-based index
    myRankings = { ...myRankings._doc, rank: myRank }; // Add rank to the object
  }

  console.log(req.query.id);

  res.render("codexRanking", {
    registrations: registrations?.ranking.splice(0, 25) ?? [],
    myRankings: req.query.id ? myRankings ?? null : null,
  });
};

module.exports.getcodexMcq = async (req, res) => {
  const question = await codexMcq.find().select("-answer");
  const questionsLength = await codexMcq.countDocuments();
  const registrations = await codexLeaderBoard
    .findOne({ name: "codexRanking" })
    .exec();

  let myRankings = registrations?.ranking.find(
    (entry) => entry?.userId?.toString() === req.query.userId
  );
  res.status(200).json({
    canGive: myRankings.score !== 0 ? myRankings.userId : false,
    questionsLength,
    questions: question,
  });
};

module.exports.codexMcq = async (req, res) => {
  res.render("codexMcq");
};

module.exports.submitMcq = async (req, res) => {
  const userAnswers = req.body.answers; // Assume answers are sent as { [questionId]: answer }
  const userId = req.body.userId; // User's id from params

  // Fetch all MCQs
  const questions = await codexMcq.find().select("answer id");
  let score = 0;

  // Compare answers
  questions.forEach((q) => {
    if (userAnswers[q.id] && userAnswers[q.id] === q.answer) {
      score += 5;
    }
  });

  // Find leaderboard and update marks for user
  const leaderboard = await codexLeaderBoard
    .findOne({ name: "codexRanking" })
    .exec();
  if (!leaderboard) {
    return res.status(404).json({ message: "Leaderboard not found" });
  }

  let userEntry = leaderboard.ranking.find(
    (entry) => entry?.userId?.toString() === req.body.userId?.toString()
  );
  if (userEntry.score && userEntry.score !== 0) {
    return res.status(400).json({ message: "You have already given the test" });
  }
  if (!userEntry) {
    return res.status(404).json({ message: "User not found in leaderboard" });
  }

  userEntry.given = true;

  // Only update if new score is greater than previous
  if (!userEntry.score || score > userEntry.score) {
    userEntry.score = score;
  }
  await leaderboard.save();

  res.status(200).json({
    message: "MCQ submitted successfully",
    score,
    userId: userEntry.userId,
  });
};

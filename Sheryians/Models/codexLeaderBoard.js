const mongoose = require("mongoose");
const crypto = require("crypto");

const codexRankingSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    ranking: [
      {
        userId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "codexRegistrations",
        },
        username: {
          type: String,
          required: true,
        },
        score: {
          type: Number,
          default: 0,
        },
        given: {
          type: Boolean,
          default: false,
        },
        avatar: {
          type: String,
          default: function () {
            const hashEmail = crypto
              .createHash("md5")
              .update(this.username || "")
              .digest("hex");
            const randomDefault = ["monsterid", "wavatar", "retro", "robohash"];
            return `https://gravatar.com/avatar/${hashEmail}?s=100&d=${
              randomDefault[Math.floor(Math.random() * randomDefault.length)]
            }`;
          },
        },
      },
    ],
  },
  {
    timestamps: true,
  }
);

codexRankingSchema.pre("save", function (next) {
  if (this.ranking && Array.isArray(this.ranking)) {
    this.ranking.sort((a, b) => b.score - a.score);
  }
  next();
});

module.exports = mongoose.model("codexLeaderBoard", codexRankingSchema);

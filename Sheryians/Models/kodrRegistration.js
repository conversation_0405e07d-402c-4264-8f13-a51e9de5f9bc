const mongoose = require("mongoose")

const kodrRegistrationSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },

  email: {
    type: String,
    required: true,
    unique: true
  },
  contact: {
    type: String,
    required: true,
    unique: true
  },

  dob: {
    type: String,
    required: true
  },
  call_status: {
    type: "String",
    enums: ["picked", "notpicked"],
    default: "notpicked"
  },
  education: { // In minutes
    type: String,
    required: true
  },
  experience: {
    type: String,
    enums: ["beginner", "intermediate", "advance"],
    require: true
  },
  status: {
    type: String,
    enums: ["onhold", "accepted", "rejected"],
    default: "onhold"
  },
  paymentStatus: {
    type: String,
    enums: ["paid", "pending"],
    default: "pending"
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('kodr', kodrRegistrationSchema);
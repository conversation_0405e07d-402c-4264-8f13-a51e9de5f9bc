const express = require("express"); // import express
const router = express.Router(); // create express router
const codexController = require("../Controllers/codex_controller");
const homeController = require("../Controllers/home_controller"); // import home controller

router.get("/", homeController.codex);
router.get("/registration", codexController.getCodex); // get home with authentication
router.post("/submission", codexController.codexRegister); //
// et home with authentication
router.get("/ranking", codexController.codexRanking); // get home with authentication

router.get("/mcq", codexController.codexMcq); // get home with authentication
router.get("/mcq/get-all-mcqs", codexController.getcodexMcq); // get home with authentication
router.post("/mcq/submit", codexController.submitMcq); // get home with authentication

module.exports = router;

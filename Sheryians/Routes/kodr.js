const express = require('express'); // import express
const router = express.Router(); // create express router
const passport = require('passport'); // import passport
const authMiddleware = require('../Config/auth.middleware'); // import isAuthenticated middleware
const kodrController = require('../Controllers/kodr_controller.js'); // import merch controller
const path = require('path'); // import path
const preventXSS = require('../utils/xss.js');


router.use(preventXSS)
router.get('/', kodrController.getKodr); // get home with authentication')
router.post('/register', kodrController.createSubmission); // get home with authentication')
router.get('/registrations', authMiddleware.isAuthenticated, authMiddleware.isAdmin, kodrController.allRegistrations); // get home with authentication')
router.post('/update', authMiddleware.isAuthenticated, authMiddleware.isAdmin, kodrController.updateStatus); // get home with authentication')
router.post('/updatecall', authMiddleware.isAuthenticated, authMiddleware.isAdmin, kodrController.updateCallStatus); // get home with authentication')
router.post('/updatepayment', authMiddleware.isAuthenticated, authMiddleware.isAdmin, kodrController.updatePaymentStatus); // update payment status

module.exports = router; // export router


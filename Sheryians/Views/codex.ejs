<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Kodex</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <link rel="stylesheet" href="../Assets/css/codex.css" />
  </head>
  <body class="text-white relative z-0 relative bg-black">
    <video
      id="heroVdo"
      class="HeroVideo"
      src="../Assets/images/cfda4326e42bda5e9a3120f31640c4f9-ezgif.com-video-speed.m4v"
      autoplay
      loop
      muted
      playsinline
    ></video>
    <div class="HeroVideoCover">
      <div class="t"></div>
      <div class="g"></div>
      <div class="b"></div>
    </div>

    <nav
      class="w-full absolute top-[0%] justify-between mb-10 flex items-center lg:pl-[3.76rem] lg:pr-[3.7rem] md:pl-[2.5rem] md:pr-[2.7rem] px-[1.5rem] pt-[2.43rem]"
    >
      <div class="logo">
        <svg
          width="157"
          height="39"
          viewBox="0 0 157 39"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M53.096 14.4763C53.096 15.6171 52.3811 16.5906 50.1147 16.5906C47.6658 16.5906 46.6619 15.4498 46.6619 13.6549H47.9091C47.9091 14.963 48.6697 15.5715 50.1299 15.5715C51.3011 15.5715 51.8487 15.2977 51.8487 14.5219C51.8487 13.5332 50.8296 13.3659 49.5975 12.9857C48.1677 12.5293 47.0117 12.0882 47.0117 10.7193C47.0117 9.51762 48.0308 8.72666 49.7648 8.72666C52.1225 8.72666 52.8374 10.0196 52.8374 11.2668H51.5901C51.5901 10.2477 50.9665 9.74578 49.7648 9.74578C48.9283 9.74578 48.259 9.98915 48.259 10.6736C48.259 11.4037 48.7762 11.6319 49.993 12.0274C51.4837 12.5141 53.096 12.8488 53.096 14.4763ZM58.9687 8.72666C60.6723 8.72666 61.7827 9.71536 61.7827 11.8144V16.4385H60.5506V11.9817C60.5506 10.3998 59.927 9.79141 58.6341 9.79141C57.037 9.79141 55.9114 11.0539 55.9114 13.0921V16.4385H54.6641V5.25863H55.9114V10.1869L55.7136 11.0539H56.0635C56.3677 9.68494 57.326 8.72666 58.9687 8.72666ZM63.4952 12.5598C63.4952 10.126 65.0163 8.72666 67.161 8.72666C69.2905 8.72666 70.6899 10.0804 70.6899 12.4076V12.8944H64.7121C64.7881 14.3242 65.4726 15.5563 67.3435 15.5563C68.5908 15.5563 69.4274 14.9478 69.7012 13.7918H70.9484C70.6594 14.963 69.7468 16.5906 67.3435 16.5906C64.636 16.5906 63.4952 14.7349 63.4952 12.5598ZM64.7121 11.9513H69.473C69.473 10.7345 68.6668 9.7762 67.161 9.7762C65.6703 9.7762 64.7121 10.7345 64.7121 11.9513ZM72.6674 16.4385V9.7762C72.6674 9.0613 73.002 8.71145 73.7169 8.74187L77.9151 8.87877V9.94352L73.9146 9.80662V16.4385H72.6674ZM85.4049 8.87877H86.7586L83.4731 16.3624V18.4767C83.4731 19.0547 83.1385 19.4046 82.4236 19.3741L79.5031 19.2372V18.1725L82.2259 18.3094V16.3624L78.9251 8.87877H80.2941L82.667 14.7653V15.3433H83.032V14.7653L85.4049 8.87877ZM90.2919 7.67713C89.6683 7.67713 89.1967 7.22081 89.1967 6.61238C89.1967 6.03438 89.6683 5.56284 90.2919 5.56284C90.9003 5.56284 91.3871 6.03438 91.3871 6.61238C91.3871 7.22081 90.9003 7.67713 90.2919 7.67713ZM87.7669 16.4385V15.3737H89.8964V9.80662L88.3297 9.94352V8.87877L90.079 8.72666C90.7939 8.66582 91.0677 9.0613 91.0677 9.6393V15.3737H93.1971V16.4385H87.7669ZM94.481 14.3394C94.481 13.1378 95.1959 12.3164 96.9604 12.1339L99.9721 11.8449C99.896 10.4607 99.1507 9.7762 97.8426 9.7762C96.7474 9.7762 95.7283 10.2629 95.7283 11.6927H94.5114C94.5114 10.126 95.6979 8.72666 97.8426 8.72666C99.9569 8.72666 101.219 10.126 101.219 12.1491V15.3737H102.33V16.4385H100.991C100.413 16.4385 100.094 16.1191 100.094 15.5563V15.1456L100.292 14.3546H99.9264C99.6374 15.5106 98.8313 16.5906 97.006 16.5906C94.8157 16.5906 94.481 15.1304 94.481 14.3394ZM95.7283 14.1873C95.7283 15.0543 96.2759 15.5258 97.1581 15.5258C98.8769 15.5258 99.9721 14.3394 99.9721 12.7727L97.1733 13.0617C96.215 13.153 95.7283 13.442 95.7283 14.1873ZM107.727 9.79141C106.13 9.79141 105.004 11.0539 105.004 13.0921V16.4385H103.757V8.87877H105.004V10.1869L104.807 11.0539H105.157C105.461 9.68494 106.419 8.72666 108.062 8.72666C109.765 8.72666 110.876 9.71536 110.876 11.8144V16.4385H109.644V11.9817C109.644 10.3998 109.02 9.79141 107.727 9.79141ZM118.87 14.4763C118.87 15.6171 118.155 16.5906 115.889 16.5906C113.44 16.5906 112.436 15.4498 112.436 13.6549H113.683C113.683 14.963 114.444 15.5715 115.904 15.5715C117.075 15.5715 117.623 15.2977 117.623 14.5219C117.623 13.5332 116.604 13.3659 115.372 12.9857C113.942 12.5293 112.786 12.0882 112.786 10.7193C112.786 9.51762 113.805 8.72666 115.539 8.72666C117.897 8.72666 118.612 10.0196 118.612 11.2668H117.364C117.364 10.2477 116.741 9.74578 115.539 9.74578C114.703 9.74578 114.033 9.98915 114.033 10.6736C114.033 11.4037 114.55 11.6319 115.767 12.0274C117.258 12.5141 118.87 12.8488 118.87 14.4763ZM46.814 27.651C46.814 25.4759 48.2286 23.7267 50.6775 23.7267C52.1377 23.7267 54.0086 24.548 54.2064 26.7384H52.9439C52.7309 25.2934 51.7727 24.7914 50.6775 24.7914C48.8826 24.7914 48.0613 26.0995 48.0613 27.651C48.0613 29.2177 48.8826 30.5258 50.6775 30.5258C51.7727 30.5258 52.7309 30.0239 52.9439 28.5789H54.2064C54.0086 30.7692 52.1377 31.5906 50.6775 31.5906C48.2286 31.5906 46.814 29.8414 46.814 27.651ZM55.6225 27.651C55.6225 25.4607 57.2044 23.7267 59.6229 23.7267C62.0262 23.7267 63.5929 25.4607 63.5929 27.651C63.5929 29.8566 62.0262 31.5906 59.6229 31.5906C57.2044 31.5906 55.6225 29.8566 55.6225 27.651ZM56.8698 27.651C56.8698 29.2329 57.8281 30.5258 59.6229 30.5258C61.4026 30.5258 62.3608 29.2329 62.3608 27.651C62.3608 26.0843 61.4026 24.7914 59.6229 24.7914C57.8281 24.7914 56.8698 26.0843 56.8698 27.651ZM73.0869 31.4385H71.8396V30.3281L72.0525 29.4763H71.6875C71.368 30.6475 70.3489 31.5906 68.6301 31.5906C66.4398 31.5906 65.0252 29.9022 65.0252 27.651C65.0252 25.415 66.4398 23.7267 68.6301 23.7267C70.3489 23.7267 71.368 24.6697 71.6875 25.8562H72.0525L71.8396 24.9892V20.2586H73.0869V31.4385ZM71.8396 27.651C71.8396 25.6432 70.5619 24.7914 68.9952 24.7914C67.4285 24.7914 66.2725 25.6432 66.2725 27.651C66.2725 29.674 67.4285 30.5258 68.9952 30.5258C70.5619 30.5258 71.8396 29.674 71.8396 27.651ZM77.3688 22.6771C76.7451 22.6771 76.2736 22.2208 76.2736 21.6124C76.2736 21.0344 76.7451 20.5628 77.3688 20.5628C77.9772 20.5628 78.4639 21.0344 78.4639 21.6124C78.4639 22.2208 77.9772 22.6771 77.3688 22.6771ZM74.8438 31.4385V30.3737H76.9733V24.8066L75.4066 24.9435V23.8788L77.1558 23.7267C77.8707 23.6658 78.1445 24.0613 78.1445 24.6393V30.3737H80.274V31.4385H74.8438ZM85.8172 24.7914C84.2201 24.7914 83.0945 26.0539 83.0945 28.0921V31.4385H81.8472V23.8788H83.0945V25.1869L82.8968 26.0539H83.2466C83.5508 24.6849 84.5091 23.7267 86.1519 23.7267C87.8555 23.7267 88.9658 24.7154 88.9658 26.8144V31.4385H87.7338V26.9817C87.7338 25.3998 87.1101 24.7914 85.8172 24.7914ZM98.7436 23.8788H99.4128V24.9435H98.774C98.3481 24.9435 98.0743 25.0348 97.7853 25.2477C98.1047 25.6432 98.3025 26.1908 98.3025 26.8449C98.3025 28.4724 97.0248 29.4915 94.7432 30.3281C96.5076 30.0391 98.2112 30.2825 98.2112 31.9556C98.2112 33.9634 96.1426 34.222 91.0774 34.222V33.1573C95.4124 33.1573 96.9639 33.0508 96.9639 31.8644C96.9639 30.1912 93.6328 30.9974 91.7619 31.5602L91.5033 30.6932C92.7202 30.3585 93.9218 30.0087 95.093 29.6436V29.2938C92.6593 29.9174 90.5298 29.1265 90.5298 26.784C90.5298 24.7001 92.2334 23.7267 94.4085 23.7267C95.3212 23.7267 96.2642 23.9548 96.9791 24.4568C97.7701 23.9548 98.0591 23.8788 98.7436 23.8788ZM94.4237 28.8527C96.0209 28.8527 97.0552 28.2899 97.0552 26.8144C97.0552 25.3542 96.0209 24.7914 94.4237 24.7914C92.8418 24.7914 91.7771 25.3542 91.7771 26.8144C91.7771 28.2899 92.8418 28.8527 94.4237 28.8527ZM111.904 29.4763C111.904 30.6171 111.189 31.5906 108.922 31.5906C106.473 31.5906 105.47 30.4498 105.47 28.6549H106.717C106.717 29.963 107.477 30.5715 108.938 30.5715C110.109 30.5715 110.656 30.2977 110.656 29.5219C110.656 28.5332 109.637 28.3659 108.405 27.9857C106.975 27.5293 105.819 27.0882 105.819 25.7193C105.819 24.5176 106.839 23.7267 108.573 23.7267C110.93 23.7267 111.645 25.0196 111.645 26.2668H110.398C110.398 25.2477 109.774 24.7458 108.573 24.7458C107.736 24.7458 107.067 24.9892 107.067 25.6736C107.067 26.4037 107.584 26.6319 108.801 27.0274C110.291 27.5141 111.904 27.8488 111.904 29.4763ZM113.182 27.651C113.182 25.4759 114.597 23.7267 117.046 23.7267C118.506 23.7267 120.377 24.548 120.575 26.7384H119.312C119.099 25.2934 118.141 24.7914 117.046 24.7914C115.251 24.7914 114.43 26.0995 114.43 27.651C114.43 29.2177 115.251 30.5258 117.046 30.5258C118.141 30.5258 119.099 30.0239 119.312 28.5789H120.575C120.377 30.7692 118.506 31.5906 117.046 31.5906C114.597 31.5906 113.182 29.8414 113.182 27.651ZM126.689 23.7267C128.393 23.7267 129.503 24.7154 129.503 26.8144V31.4385H128.271V26.9817C128.271 25.3998 127.647 24.7914 126.354 24.7914C124.757 24.7914 123.632 26.0539 123.632 28.0921V31.4385H122.384V20.2586H123.632V25.1869L123.434 26.0539H123.784C124.088 24.6849 125.046 23.7267 126.689 23.7267ZM131.215 27.651C131.215 25.4607 132.797 23.7267 135.216 23.7267C137.619 23.7267 139.186 25.4607 139.186 27.651C139.186 29.8566 137.619 31.5906 135.216 31.5906C132.797 31.5906 131.215 29.8566 131.215 27.651ZM132.463 27.651C132.463 29.2329 133.421 30.5258 135.216 30.5258C136.995 30.5258 137.954 29.2329 137.954 27.651C137.954 26.0843 136.995 24.7914 135.216 24.7914C133.421 24.7914 132.463 26.0843 132.463 27.651ZM140.618 27.651C140.618 25.4607 142.2 23.7267 144.618 23.7267C147.022 23.7267 148.588 25.4607 148.588 27.651C148.588 29.8566 147.022 31.5906 144.618 31.5906C142.2 31.5906 140.618 29.8566 140.618 27.651ZM141.865 27.651C141.865 29.2329 142.824 30.5258 144.618 30.5258C146.398 30.5258 147.356 29.2329 147.356 27.651C147.356 26.0843 146.398 24.7914 144.618 24.7914C142.824 24.7914 141.865 26.0843 141.865 27.651ZM149.349 31.4385V30.3737H151.478V21.1865L149.912 21.3234V20.2586L151.676 20.1065C152.376 20.0457 152.71 20.4412 152.71 21.1561V30.3737H154.825V31.4385H149.349Z"
            fill="white"
          />
          <line
            x1="41.2191"
            y1="5.43848"
            x2="41.2191"
            y2="34.7241"
            stroke="white"
            stroke-width="0.563185"
          />
          <path
            d="M9.55556 18.7654C9.14558 18.6132 8.73888 18.4617 8.3342 18.311C7.57197 18.027 6.81692 17.7458 6.06066 17.4678C5.87433 17.3993 5.74595 17.3 5.64886 17.1177C4.83662 15.5929 4.01663 14.0722 3.1903 12.555C3.07318 12.34 3.069 12.1596 3.15412 11.9258C3.48081 11.0285 3.74959 10.1127 4.01838 9.19681C4.13973 8.78333 4.26108 8.36985 4.38776 7.95805C4.58101 7.32987 4.7677 6.69948 4.94401 6.06638C4.99443 5.88534 5.08556 5.75256 5.22858 5.63759C5.39411 5.50454 5.56005 5.37195 5.726 5.23937C6.29488 4.78484 6.86382 4.33027 7.41627 3.85651C7.69443 3.61797 8.0152 3.5714 8.33757 3.5246C8.39158 3.51676 8.44564 3.50892 8.49955 3.50016C10.1662 3.22937 11.8339 2.96386 13.5039 2.7149C13.8187 2.66798 13.9158 2.52532 13.92 2.2288C13.9308 1.469 13.9516 1.45701 14.5957 1.85268C16.4284 2.9785 18.2592 4.10762 20.0839 5.24627C20.3206 5.39396 20.4525 5.38951 20.6268 5.15238C20.8508 4.84742 21.0867 4.55117 21.3226 4.25491C21.5261 3.99932 21.7296 3.74373 21.9256 3.4825C22.0803 3.27622 22.2699 3.18017 22.5095 3.13808C22.6331 3.11637 22.7567 3.09483 22.8803 3.0733C23.4039 2.98209 23.9276 2.89086 24.4485 2.78618C24.7832 2.71893 24.9723 2.79198 25.0555 3.15756C25.1046 3.37297 25.1759 3.58326 25.2473 3.79359C25.3157 3.99533 25.3842 4.1971 25.433 4.40346C25.5088 4.72405 25.6547 4.81053 25.9725 4.75395C27.0464 4.56277 28.1234 4.38799 29.2016 4.22168C29.2478 4.21455 29.2975 4.20043 29.3483 4.18602C29.5113 4.13978 29.6851 4.09046 29.7876 4.25873C29.8747 4.4017 29.7714 4.54072 29.6743 4.67136C29.6443 4.7117 29.6149 4.75125 29.5918 4.78986C29.3453 5.20364 29.0915 5.61492 28.8115 6.0063C28.6444 6.23982 28.6828 6.37893 28.8823 6.5669C29.2023 6.86831 29.5156 7.17729 29.8289 7.48621C30.4122 8.06132 30.9953 8.63625 31.6201 9.16183C32.1809 9.63357 32.3403 10.1758 32.3754 10.8382C32.397 11.2464 32.3488 11.6188 32.1631 11.9939C31.7671 12.794 31.4021 13.6097 31.0394 14.4257C30.9247 14.6836 30.7914 14.766 30.5245 14.6322C30.307 14.5232 30.081 14.4309 29.8552 14.3387C29.787 14.3109 29.7189 14.2831 29.6511 14.2548C29.484 14.1852 29.43 14.0811 29.4707 13.8946C29.6108 13.2531 29.7377 12.6087 29.8626 11.964C29.8912 11.8164 29.9698 11.7075 30.0654 11.6017C30.1779 11.4772 30.2888 11.3514 30.4197 11.2029C30.48 11.1345 30.5445 11.0612 30.6154 10.9811C30.376 10.9178 30.1518 10.8123 29.9317 10.7087C29.4153 10.4657 28.9218 10.2335 28.3091 10.5816C28.2585 10.6103 28.203 10.6302 28.1475 10.6501C28.0916 10.6701 28.0358 10.6901 27.985 10.7191C27.2071 11.1631 26.5322 10.9554 25.9068 10.4028C25.8807 10.3796 25.8466 10.3655 25.803 10.3474C25.7794 10.3376 25.7531 10.3267 25.7238 10.3126C25.5289 10.7339 25.4304 11.167 25.3441 11.5967C25.3151 11.741 25.4413 11.8292 25.5599 11.9121C25.5963 11.9375 25.6321 11.9625 25.6624 11.9885C26.7495 12.92 27.8392 13.8485 28.9289 14.777C29.3066 15.0988 29.6842 15.4206 30.0618 15.7425C30.1674 15.8326 30.2732 15.9226 30.379 16.0126C30.7299 16.3111 31.0808 16.6096 31.4278 16.9125C31.8701 17.2986 31.7687 17.9797 31.2417 18.1977C30.9459 18.32 30.6499 18.4419 30.3539 18.5639C29.607 18.8716 28.86 19.1793 28.1157 19.4934C27.8662 19.5987 27.6339 19.6265 27.3661 19.5567C26.9788 19.4559 26.5891 19.3642 26.1994 19.2724C25.8465 19.1894 25.4936 19.1064 25.1424 19.0165C24.9176 18.959 24.7167 18.9706 24.5424 19.1106C23.8549 19.6627 23.211 19.6514 22.497 19.1026C22.0933 18.7924 21.7164 18.5008 21.4278 18.0677C19.9925 15.9141 18.5434 13.7697 17.0943 11.6253C16.7795 11.1595 16.4647 10.6937 16.1501 10.2278C16.1344 10.2046 16.1195 10.1804 16.1045 10.156C16.0403 10.0516 15.9743 9.94422 15.8364 9.89889C15.6477 9.99486 15.5281 10.1605 15.4097 10.3244C15.3781 10.3681 15.3466 10.4117 15.3139 10.4539C15.1804 10.6259 15.0285 10.6769 14.8242 10.645C14.5241 10.598 14.2237 10.5529 13.9232 10.5078C13.2842 10.4118 12.6452 10.3159 12.0093 10.202C11.7264 10.1513 11.4918 10.1777 11.2912 10.3649C10.8675 10.76 10.3713 10.752 9.8414 10.6931C9.74881 10.6829 9.65597 10.6703 9.56294 10.6578C9.24378 10.6147 8.92234 10.5713 8.60109 10.6176C8.56563 10.796 8.6615 10.8756 8.74916 10.9485C8.76697 10.9633 8.78444 10.9778 8.80041 10.9928C9.10816 11.2822 9.41838 11.5689 9.72861 11.8557C9.99674 12.1035 10.2649 12.3514 10.5314 12.6009C10.6696 12.7303 10.8279 12.7818 11.0101 12.7903C11.3457 12.8058 11.6813 12.822 12.0169 12.8381C12.7253 12.8721 13.4337 12.9061 14.1423 12.935C14.361 12.9439 14.5444 13.0141 14.7394 13.11C15.8122 13.6371 16.6313 14.4322 17.2963 15.4137C17.4968 15.7095 17.7216 15.9886 17.9466 16.2679C18.0687 16.4194 18.1907 16.571 18.309 16.7252C18.5697 17.0654 18.5234 17.2198 18.1231 17.3206C17.1786 17.5584 16.2339 17.7954 15.2892 18.0324C13.8312 18.3981 12.3732 18.7638 10.916 19.1328C10.7568 19.1731 10.6202 19.164 10.473 19.1065C10.2361 19.014 9.99712 18.9267 9.74484 18.8346C9.68261 18.8119 9.61958 18.7888 9.55556 18.7654Z"
            fill="white"
          />
          <path
            d="M23.4111 21.8196C23.6012 21.8656 23.7882 21.9118 23.9732 21.9575C24.3591 22.0528 24.7363 22.146 25.1154 22.2306C25.3099 22.274 25.4595 22.3592 25.5884 22.5079C25.6014 22.5229 25.6144 22.5379 25.6274 22.5529C26.0836 23.0795 26.54 23.6062 27.0091 24.1213C27.1609 24.288 27.207 24.4412 27.137 24.6588C26.95 25.2395 26.7742 25.8241 26.6094 26.4114C26.5569 26.5981 26.4524 26.7094 26.2918 26.8055C24.0945 28.1199 21.8975 29.4349 19.7078 30.7621C19.3907 30.9543 19.1762 30.9446 18.9225 30.6522C18.2182 29.8404 17.4985 29.0418 16.7789 28.2434C16.6412 28.0906 16.5035 27.9379 16.366 27.785C16.2329 27.6372 16.1666 27.4826 16.1697 27.2789C16.1795 26.6329 16.1798 25.9865 16.1697 25.3404C16.1664 25.1275 16.2282 24.9572 16.3571 24.7913C17.2941 23.5853 18.2307 22.379 19.1549 21.1633C19.3419 20.9174 19.5533 20.8677 19.8302 20.9366C21.0138 21.2311 22.1983 21.522 23.4111 21.8196Z"
            fill="white"
          />
          <path
            d="M27.8991 27.9604C28.089 27.8088 28.2571 27.8272 28.4452 27.9168C29.2193 28.2856 29.9967 28.6473 30.7757 29.0057C30.9258 29.0748 31.0071 29.1666 31.052 29.3347C31.364 30.5018 31.6884 31.6656 32.0133 32.8292C32.09 33.1041 32.0356 33.3271 31.8326 33.5354C30.8076 34.587 29.7855 35.6416 28.7732 36.7055C28.6024 36.885 28.4347 36.8701 28.2331 36.8181C27.9008 36.7323 27.5677 36.6491 27.2347 36.566C26.5321 36.3906 25.8296 36.2152 25.1343 36.0149C24.5612 35.8498 24.0113 35.7985 23.45 36.0118C23.1239 36.1358 22.966 36.0225 22.8647 35.6894C22.5541 34.6678 22.2249 33.6513 21.8792 32.641C21.7663 32.3111 21.8561 32.1221 22.1302 31.9345C23.5792 30.9431 25.024 29.9455 26.4781 28.9413C26.9506 28.615 27.4241 28.288 27.8991 27.9604Z"
            fill="white"
          />
          <path
            d="M32.7428 20.6878C32.8112 21.621 32.876 22.5214 32.9393 23.422C32.9545 23.6388 32.9689 23.8556 32.9833 24.0724C33.0085 24.4522 33.0337 24.832 33.0634 25.2115C33.083 25.4615 33.0719 25.6983 32.9616 25.9321C32.7111 26.463 32.4679 26.9976 32.2342 27.5361C32.1051 27.8335 31.9218 27.8577 31.6812 27.6829C31.353 27.4445 31.0252 27.2054 30.6975 26.9663C30.2092 26.6102 29.721 26.254 29.2312 25.9001C29.0692 25.783 29.0292 25.6272 29.0207 25.4423C28.9909 24.7977 28.958 24.1532 28.9171 23.5092C28.9072 23.354 28.9632 23.2394 29.0554 23.1257C29.738 22.2837 30.4199 21.4409 31.1017 20.5982C31.4467 20.1717 31.7918 19.7452 32.137 19.3188C32.1389 19.3164 32.1409 19.3139 32.1429 19.3115C32.2385 19.1932 32.3415 19.0658 32.5155 19.1316C32.6535 19.1837 32.6562 19.3076 32.6588 19.4279C32.6593 19.4512 32.6598 19.4745 32.6613 19.497C32.6868 19.883 32.7127 20.2691 32.7428 20.6878Z"
            fill="white"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M8.3342 18.311C7.57197 18.027 6.81692 17.7458 6.06066 17.4678C5.87433 17.3993 5.74595 17.3 5.64886 17.1177C4.83662 15.5929 4.01663 14.0722 3.1903 12.555C3.07318 12.34 3.069 12.1596 3.15412 11.9258C3.48081 11.0285 3.74959 10.1127 4.01838 9.19681C4.13973 8.78333 4.26108 8.36985 4.38776 7.95805C4.58101 7.32987 4.7677 6.69948 4.94401 6.06638C4.99443 5.88534 5.08556 5.75256 5.22858 5.63759C5.39411 5.50454 5.56005 5.37195 5.726 5.23937C6.29488 4.78484 6.86382 4.33027 7.41627 3.85651C7.69443 3.61797 8.0152 3.5714 8.33757 3.5246C8.39158 3.51676 8.44564 3.50892 8.49955 3.50016C10.1662 3.22937 11.8339 2.96386 13.5039 2.7149C13.8187 2.66798 13.9158 2.52532 13.92 2.2288C13.9308 1.469 13.9516 1.45701 14.5957 1.85268C16.4284 2.9785 18.2592 4.10762 20.0839 5.24627C20.3206 5.39396 20.4525 5.38951 20.6268 5.15238C20.8508 4.84742 21.0867 4.55117 21.3226 4.25491C21.5261 3.99932 21.7296 3.74373 21.9256 3.4825C22.0803 3.27622 22.2699 3.18017 22.5095 3.13808C22.6331 3.11637 22.7567 3.09483 22.8803 3.0733C23.4039 2.98209 23.9276 2.89086 24.4485 2.78618C24.7832 2.71893 24.9723 2.79198 25.0555 3.15756C25.1046 3.37297 25.1759 3.58326 25.2473 3.79359C25.3157 3.99533 25.3842 4.1971 25.433 4.40346C25.5088 4.72405 25.6547 4.81053 25.9725 4.75395C27.0464 4.56277 28.1234 4.38799 29.2016 4.22168C29.2478 4.21455 29.2975 4.20043 29.3483 4.18602C29.5113 4.13978 29.6851 4.09046 29.7876 4.25873C29.8747 4.4017 29.7714 4.54072 29.6743 4.67136C29.6443 4.7117 29.6149 4.75125 29.5918 4.78986C29.3453 5.20364 29.0915 5.61492 28.8115 6.0063C28.6444 6.23982 28.6828 6.37893 28.8823 6.5669C29.2023 6.86831 29.5156 7.17729 29.8289 7.48621C30.4122 8.06132 30.9953 8.63625 31.6201 9.16183C32.1809 9.63357 32.3403 10.1758 32.3754 10.8382C32.397 11.2464 32.3488 11.6188 32.1631 11.9939C31.7671 12.794 31.4021 13.6097 31.0394 14.4257C30.9247 14.6836 30.7914 14.766 30.5245 14.6322C30.307 14.5232 30.081 14.4309 29.8552 14.3387C29.787 14.3109 29.7189 14.2831 29.6511 14.2548C29.484 14.1852 29.43 14.0811 29.4707 13.8946C29.6108 13.2531 29.7377 12.6087 29.8626 11.964C29.8912 11.8164 29.9698 11.7075 30.0654 11.6017C30.1779 11.4772 30.2888 11.3514 30.4197 11.2029C30.48 11.1345 30.5445 11.0612 30.6154 10.9811C30.376 10.9178 30.1518 10.8123 29.9317 10.7087C29.4153 10.4657 28.9218 10.2335 28.3091 10.5816C28.2585 10.6103 28.203 10.6302 28.1475 10.6501C28.0916 10.6701 28.0358 10.6901 27.985 10.7191C27.2071 11.1631 26.5322 10.9554 25.9068 10.4028C25.8807 10.3796 25.8466 10.3655 25.803 10.3474C25.7794 10.3376 25.7531 10.3267 25.7238 10.3126C25.5289 10.7339 25.4304 11.167 25.3441 11.5967C25.3151 11.741 25.4413 11.8292 25.5599 11.9121C25.5963 11.9375 25.6321 11.9625 25.6624 11.9885C26.7495 12.92 27.8392 13.8485 28.9289 14.777C29.3066 15.0988 29.6842 15.4206 30.0618 15.7425C30.1674 15.8326 30.2732 15.9226 30.379 16.0126C30.7299 16.3111 31.0808 16.6096 31.4278 16.9125C31.8701 17.2986 31.7687 17.9797 31.2417 18.1977C30.9459 18.32 30.6499 18.4419 30.3539 18.5639C29.607 18.8716 28.86 19.1793 28.1157 19.4934C27.8662 19.5987 27.6339 19.6265 27.3661 19.5567C26.9788 19.4559 26.5891 19.3642 26.1994 19.2724C25.8465 19.1894 25.4936 19.1064 25.1424 19.0165C24.9176 18.959 24.7167 18.9706 24.5424 19.1106C23.8549 19.6627 23.211 19.6514 22.497 19.1026C22.0933 18.7924 21.7164 18.5008 21.4278 18.0677C19.9925 15.9141 18.5434 13.7697 17.0943 11.6253C16.7795 11.1595 16.4647 10.6937 16.1501 10.2278C16.1344 10.2046 16.1195 10.1804 16.1045 10.156C16.0403 10.0516 15.9743 9.94422 15.8364 9.89889C15.6477 9.99486 15.5281 10.1605 15.4097 10.3244C15.3781 10.3681 15.3466 10.4117 15.3139 10.4539C15.1804 10.6259 15.0285 10.6769 14.8242 10.645C14.5241 10.598 14.2237 10.5529 13.9232 10.5078C13.2842 10.4118 12.6452 10.3159 12.0093 10.202C11.7264 10.1513 11.4918 10.1777 11.2912 10.3649C10.8675 10.76 10.3713 10.752 9.8414 10.6931C9.74881 10.6829 9.65597 10.6703 9.56294 10.6578C9.24378 10.6147 8.92234 10.5713 8.60109 10.6176C8.56563 10.796 8.6615 10.8756 8.74916 10.9485C8.76697 10.9633 8.78444 10.9778 8.80041 10.9928C9.10816 11.2822 9.41838 11.5689 9.72861 11.8557C9.99674 12.1035 10.2649 12.3514 10.5314 12.6009C10.6696 12.7303 10.8279 12.7818 11.0101 12.7903C11.3457 12.8058 11.6813 12.822 12.0169 12.8381C12.7253 12.8721 13.4337 12.9061 14.1423 12.935C14.361 12.9439 14.5444 13.0141 14.7394 13.11C15.8122 13.6371 16.6313 14.4322 17.2963 15.4137C17.4968 15.7095 17.7216 15.9886 17.9466 16.2679C18.0687 16.4194 18.1907 16.571 18.309 16.7252C18.5697 17.0654 18.5234 17.2198 18.1231 17.3206C17.1786 17.5584 16.2339 17.7954 15.2892 18.0324C13.8312 18.3981 12.3732 18.7638 10.916 19.1328C10.7568 19.1731 10.6202 19.164 10.473 19.1065C10.2361 19.014 9.99712 18.9267 9.74484 18.8346C9.68261 18.8119 9.61958 18.7888 9.55556 18.7654ZM23.9732 21.9575C24.3591 22.0528 24.7363 22.146 25.1154 22.2306C25.3099 22.274 25.4595 22.3592 25.5884 22.5079L25.6274 22.5529C26.0836 23.0795 26.54 23.6062 27.0091 24.1213C27.1609 24.288 27.207 24.4412 27.137 24.6588C26.95 25.2395 26.7742 25.8241 26.6094 26.4114C26.5569 26.5981 26.4524 26.7094 26.2918 26.8055C24.0945 28.1199 21.8975 29.4349 19.7078 30.7621C19.3907 30.9543 19.1762 30.9446 18.9225 30.6522C18.2182 29.8404 17.4985 29.0418 16.7789 28.2434C16.6412 28.0906 16.5035 27.9379 16.366 27.785C16.2329 27.6372 16.1666 27.4826 16.1697 27.2789C16.1795 26.6329 16.1798 25.9865 16.1697 25.3404C16.1664 25.1275 16.2282 24.9572 16.3571 24.7913C17.2941 23.5853 18.2307 22.379 19.1549 21.1633C19.3419 20.9174 19.5533 20.8677 19.8302 20.9366C21.0138 21.2311 22.1983 21.522 23.4111 21.8196ZM27.8991 27.9604C28.089 27.8088 28.2571 27.8272 28.4452 27.9168C29.2193 28.2856 29.9967 28.6473 30.7757 29.0057C30.9258 29.0748 31.0071 29.1666 31.052 29.3347C31.364 30.5018 31.6884 31.6656 32.0133 32.8292C32.09 33.1041 32.0356 33.3271 31.8326 33.5354C30.8076 34.587 29.7855 35.6416 28.7732 36.7055C28.6024 36.885 28.4347 36.8701 28.2331 36.8181C27.9008 36.7323 27.5677 36.6491 27.2347 36.566C26.5321 36.3906 25.8296 36.2152 25.1343 36.0149C24.5612 35.8498 24.0113 35.7985 23.45 36.0118C23.1239 36.1358 22.966 36.0225 22.8647 35.6894C22.5541 34.6678 22.2249 33.6513 21.8792 32.641C21.7663 32.3111 21.8561 32.1221 22.1302 31.9345C23.5792 30.9431 25.024 29.9455 26.4781 28.9413C26.9506 28.615 27.4241 28.288 27.8991 27.9604ZM32.7428 20.6878C32.8112 21.621 32.876 22.5214 32.9393 23.422C32.9545 23.6388 32.9689 23.8556 32.9833 24.0724C33.0085 24.4522 33.0337 24.832 33.0634 25.2115C33.083 25.4615 33.0719 25.6983 32.9616 25.9321C32.7111 26.463 32.4679 26.9976 32.2342 27.5361C32.1051 27.8335 31.9218 27.8577 31.6812 27.6829C31.353 27.4445 31.0252 27.2054 30.6975 26.9663C30.2092 26.6102 29.721 26.254 29.2312 25.9001C29.0692 25.783 29.0292 25.6272 29.0207 25.4423C28.9909 24.7977 28.958 24.1532 28.9171 23.5092C28.9072 23.354 28.9632 23.2394 29.0554 23.1257C29.738 22.2837 30.4199 21.4409 31.1017 20.5982C31.4467 20.1717 31.7918 19.7452 32.137 19.3188L32.1429 19.3115C32.2385 19.1932 32.3415 19.0658 32.5155 19.1316C32.6535 19.1837 32.6562 19.3076 32.6588 19.4279C32.6593 19.4512 32.6598 19.4745 32.6613 19.497C32.6868 19.883 32.7127 20.2691 32.7428 20.6878Z"
            fill="white"
          />
        </svg>
      </div>
      <ul
        class="border md:flex hidden font-[urbanians-regular] tracking-[.4px] lg:mr-[3.2rem] mt-2.5 flex gap-9 border-white/[0.1] transition-all ease duration-500 bg-grad-hover text-[.8rem] bg-grad inner-shadow px-[2rem] font-thin py-[.58rem] rounded-full text-white/50 transition-all relative z-[9999999]"
      >
        <li>
          <a class="cursor-pointer hover:text-white relative" href="/">Home</a>
        </li>
        <li>
          <a class="cursor-pointer hover:text-white relative" href="/courses"
            >Courses</a
          >
        </li>
        <li>
          <a class="cursor-pointer hover:text-white relative" href="/kodex/ranking"
            >Ranking</a
          >
        </li>
      </ul>
      <div class="cta">
        <a href="/kodex/registration">
          <button
            class="border font-[urbanians-regular] tracking-[.4px] mt-2.5 flex gap-9 border-white/[0.1] transition-all ease duration-500 bg-grad-hover text-[.8rem] bg-grad inner-shadow px-[2rem] font-thin py-[.58rem] rounded-full text-white/50 transition-all hover:text-white relative z-[9999999]"
          >
            Enroll Now
          </button>
        </a>
      </div>
    </nav>
    <div class="page1 px-5 w-full overflow-hidden relative flex-col justify-center">
      <svg
        class="absolute bottom-0 left-0 z-0"
        width="1209"
        height="455"
        viewBox="0 0 1209 455"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M139.113 354.689C139.306 354.212 138.893 353.75 138.465 353.965C138.373 354.013 138.301 354.092 138.259 354.196C138.059 354.673 138.479 355.134 138.907 354.919C138.999 354.872 139.071 354.792 139.113 354.689Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M481.865 382.311C482.058 381.834 481.643 381.373 481.215 381.588C481.122 381.635 481.051 381.715 481.008 381.818C480.815 382.295 481.229 382.756 481.658 382.542C481.751 382.494 481.822 382.414 481.865 382.311Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M407.875 336.782C408.067 336.305 407.654 335.844 407.226 336.058C407.134 336.106 407.063 336.186 407.02 336.289C406.821 336.766 407.241 337.227 407.668 337.012C407.761 336.965 407.832 336.885 407.875 336.782Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M307.378 118.023C307.577 117.546 307.157 117.085 306.73 117.299C306.637 117.347 306.566 117.426 306.523 117.53C306.331 118.007 306.744 118.468 307.171 118.253C307.264 118.206 307.335 118.126 307.378 118.023Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M369.354 105.777C369.553 105.301 369.133 104.833 368.705 105.055C368.613 105.103 368.542 105.182 368.499 105.285C368.307 105.761 368.72 106.221 369.147 106.007C369.24 105.959 369.311 105.88 369.354 105.777Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1092.05 7.43127C1092.24 6.95422 1091.83 6.49306 1091.4 6.70773C1091.3 6.75544 1091.23 6.83495 1091.19 6.93831C1091 7.41537 1091.41 7.87653 1091.84 7.66185C1091.93 7.61414 1092 7.53463 1092.05 7.43127Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M772.383 401.59C772.576 401.113 772.162 400.651 771.733 400.866C771.64 400.914 771.569 400.993 771.526 401.097C771.333 401.574 771.748 402.035 772.176 401.82C772.269 401.773 772.34 401.693 772.383 401.59Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M857.285 428.812C857.484 428.335 857.064 427.874 856.637 428.089C856.544 428.136 856.473 428.216 856.43 428.319C856.238 428.796 856.651 429.258 857.078 429.043C857.171 428.995 857.242 428.916 857.285 428.812Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1032.59 320.2C1032.79 319.723 1032.37 319.262 1031.95 319.477C1031.85 319.525 1031.78 319.604 1031.74 319.708C1031.55 320.185 1031.96 320.646 1032.39 320.431C1032.48 320.383 1032.55 320.304 1032.59 320.2Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1073.08 147.53C1073.28 147.053 1072.86 146.592 1072.43 146.806C1072.34 146.854 1072.27 146.934 1072.23 147.037C1072.03 147.514 1072.45 147.975 1072.87 147.761C1072.97 147.713 1073.04 147.633 1073.08 147.53Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1031.73 17.2374C1031.93 16.7603 1031.51 16.2992 1031.08 16.5138C1030.99 16.5615 1030.92 16.641 1030.88 16.7444C1030.68 17.2215 1031.1 17.6826 1031.53 17.4679C1031.62 17.4202 1031.69 17.3407 1031.73 17.2374Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1103.49 48.8655C1103.69 48.3884 1103.27 47.9272 1102.84 48.1419C1102.75 48.1896 1102.68 48.2691 1102.64 48.3725C1102.44 48.8495 1102.86 49.3107 1103.29 49.096C1103.38 49.0483 1103.45 48.9688 1103.49 48.8655Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1025.25 74.3632C1025.45 73.8862 1025.03 73.425 1024.6 73.6397C1024.51 73.6874 1024.44 73.7669 1024.39 73.8703C1024.2 74.3473 1024.61 74.8085 1025.04 74.5938C1025.13 74.5461 1025.21 74.4666 1025.25 74.3632Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1027.15 188.042C1027.35 187.565 1026.93 187.104 1026.51 187.319C1026.41 187.367 1026.34 187.446 1026.3 187.55C1026.11 188.027 1026.52 188.488 1026.95 188.273C1027.04 188.225 1027.11 188.146 1027.15 188.042Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1177.92 206.104C1178.12 205.627 1177.7 205.166 1177.28 205.38C1177.18 205.428 1177.11 205.508 1177.07 205.611C1176.87 206.088 1177.29 206.549 1177.72 206.335C1177.81 206.287 1177.88 206.207 1177.92 206.104Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M377.336 215.666C377.528 215.189 377.115 214.728 376.688 214.942C376.595 214.99 376.524 215.07 376.481 215.173C376.282 215.65 376.702 216.111 377.13 215.896C377.222 215.849 377.293 215.769 377.336 215.666Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M618.753 373.967C618.946 373.49 618.532 373.029 618.103 373.243C618.01 373.291 617.939 373.37 617.896 373.474C617.703 373.951 618.117 374.412 618.546 374.197C618.639 374.15 618.71 374.07 618.753 373.967Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M489.344 332.576C489.544 332.099 489.123 331.638 488.696 331.853C488.604 331.901 488.532 331.98 488.49 332.083C488.297 332.56 488.71 333.022 489.138 332.807C489.23 332.759 489.302 332.68 489.344 332.576Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M246.467 396.041C246.659 395.564 246.246 395.102 245.819 395.317C245.726 395.365 245.655 395.444 245.612 395.548C245.413 396.025 245.833 396.486 246.26 396.271C246.353 396.224 246.424 396.144 246.467 396.041Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M821.046 9.55593C821.246 9.07887 820.825 8.61772 820.398 8.8324C820.305 8.88011 820.234 8.95961 820.192 9.06297C819.999 9.54003 820.412 10.0012 820.84 9.78651C820.932 9.73881 821.003 9.65929 821.046 9.55593Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M379.246 431.338C379.439 430.861 379.026 430.399 378.598 430.614C378.506 430.662 378.434 430.741 378.392 430.845C378.192 431.322 378.613 431.783 379.04 431.568C379.132 431.521 379.204 431.441 379.246 431.338Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M253.285 58.4274C253.484 57.9503 253.064 57.4892 252.637 57.7038C252.544 57.7515 252.473 57.831 252.43 57.9344C252.238 58.4115 252.651 58.8726 253.078 58.6579C253.171 58.6102 253.242 58.5307 253.285 58.4274Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M370.656 19.1174C370.848 18.6403 370.435 18.1792 370.008 18.3938C369.915 18.4415 369.844 18.521 369.801 18.6244C369.602 19.1015 370.022 19.5626 370.449 19.3479C370.542 19.3002 370.613 19.2207 370.656 19.1174Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M94.887 296.409C95.0798 295.932 94.6656 295.471 94.2372 295.686C94.1443 295.734 94.0729 295.813 94.0301 295.917C93.8372 296.394 94.2514 296.855 94.6799 296.64C94.7727 296.592 94.8442 296.513 94.887 296.409Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M151.775 275.317C151.975 274.84 151.554 274.378 151.127 274.593C151.034 274.641 150.963 274.72 150.92 274.824C150.728 275.301 151.141 275.762 151.569 275.547C151.661 275.5 151.732 275.42 151.775 275.317Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M159.773 151.92C159.966 151.443 159.552 150.982 159.123 151.197C159.03 151.245 158.959 151.324 158.916 151.427C158.723 151.905 159.137 152.366 159.566 152.151C159.659 152.103 159.73 152.024 159.773 151.92Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M153.097 208.229C153.289 207.752 152.876 207.29 152.449 207.505C152.356 207.553 152.285 207.632 152.242 207.736C152.043 208.213 152.463 208.674 152.89 208.459C152.983 208.412 153.054 208.332 153.097 208.229Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M238.876 435.776C239.068 435.299 238.655 434.838 238.228 435.053C238.135 435.1 238.064 435.18 238.021 435.283C237.822 435.76 238.242 436.221 238.669 436.007C238.762 435.959 238.833 435.88 238.876 435.776Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M369.025 277C369.218 276.523 368.804 276.061 368.375 276.276C368.283 276.324 368.211 276.403 368.168 276.507C367.976 276.984 368.39 277.445 368.818 277.23C368.911 277.183 368.982 277.103 369.025 277Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M336.954 234.393C337.147 233.916 336.732 233.455 336.304 233.67C336.211 233.717 336.14 233.797 336.097 233.9C335.904 234.377 336.318 234.839 336.747 234.624C336.84 234.576 336.911 234.497 336.954 234.393Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M209.696 156.401C210.125 156.615 210.539 156.155 210.346 155.679C210.303 155.576 210.232 155.497 210.139 155.449C209.71 155.227 209.296 155.695 209.489 156.171C209.532 156.274 209.603 156.353 209.696 156.401Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M249.848 316.107C250.04 315.63 249.627 315.169 249.2 315.384C249.107 315.432 249.036 315.511 248.993 315.614C248.794 316.091 249.214 316.553 249.641 316.338C249.734 316.29 249.805 316.211 249.848 316.107Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M551 362.28C551.192 361.803 550.779 361.342 550.352 361.557C550.259 361.604 550.188 361.684 550.145 361.787C549.946 362.264 550.366 362.725 550.793 362.511C550.886 362.463 550.957 362.384 551 362.28Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M683.643 444.085C683.835 443.609 683.422 443.149 682.995 443.363C682.902 443.411 682.831 443.49 682.788 443.593C682.589 444.069 683.009 444.537 683.437 444.314C683.529 444.267 683.6 444.188 683.643 444.085Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M618.753 411.152C618.946 410.674 618.532 410.213 618.103 410.428C618.01 410.476 617.939 410.555 617.896 410.659C617.703 411.136 618.117 411.597 618.546 411.382C618.639 411.334 618.71 411.255 618.753 411.152Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M907.361 263.435C907.553 262.958 907.14 262.497 906.713 262.712C906.62 262.759 906.549 262.839 906.506 262.942C906.307 263.419 906.727 263.88 907.154 263.666C907.247 263.618 907.318 263.538 907.361 263.435Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M362.497 94.3256C362.689 93.8486 362.276 93.3874 361.849 93.6021C361.756 93.6498 361.685 93.7293 361.642 93.8327C361.443 94.3097 361.863 94.7709 362.29 94.5562C362.383 94.5085 362.454 94.429 362.497 94.3256Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M975.916 114.761C976.108 114.284 975.695 113.823 975.268 114.037C975.175 114.085 975.104 114.165 975.061 114.268C974.862 114.745 975.282 115.206 975.71 114.991C975.802 114.944 975.873 114.864 975.916 114.761Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M989.969 27.0161C990.162 26.5403 989.749 26.0804 989.321 26.2945C989.229 26.3421 989.158 26.4214 989.115 26.5245C988.915 27.0003 989.336 27.4681 989.763 27.2461C989.855 27.1985 989.927 27.1192 989.969 27.0161Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M924.2 220.433C924.393 219.956 923.979 219.495 923.552 219.71C923.46 219.757 923.388 219.837 923.346 219.94C923.146 220.417 923.566 220.878 923.994 220.664C924.086 220.616 924.158 220.537 924.2 220.433Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M92.9768 9.55386C93.1691 9.07808 92.756 8.61815 92.3287 8.83225C92.2361 8.87983 92.1648 8.95914 92.1221 9.06222C91.9227 9.538 92.3429 10.0059 92.7702 9.78383C92.8628 9.73625 92.934 9.65695 92.9768 9.55386Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1108.06 83.2044C1107.64 82.9824 1107.22 83.4502 1107.41 83.926C1107.46 84.0291 1107.53 84.1084 1107.62 84.156C1108.05 84.3701 1108.46 83.9102 1108.27 83.4344C1108.23 83.3313 1108.16 83.252 1108.06 83.2044Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M627.341 21.242C627.541 20.7649 627.121 20.3038 626.693 20.5185C626.601 20.5662 626.529 20.6457 626.487 20.749C626.294 21.2261 626.707 21.6873 627.135 21.4726C627.227 21.4249 627.299 21.3454 627.341 21.242Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M481.97 299.015C482.163 298.538 481.748 298.077 481.32 298.291C481.227 298.339 481.156 298.418 481.113 298.522C480.92 298.999 481.334 299.46 481.763 299.245C481.856 299.198 481.927 299.118 481.97 299.015Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M539.623 34.3434C539.815 33.8677 539.402 33.4077 538.975 33.6218C538.882 33.6694 538.811 33.7487 538.768 33.8518C538.569 34.3276 538.989 34.7954 539.417 34.5734C539.509 34.5258 539.58 34.4465 539.623 34.3434Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M949.869 332.532C950.068 332.055 949.648 331.594 949.221 331.809C949.128 331.856 949.057 331.936 949.014 332.039C948.822 332.516 949.235 332.977 949.662 332.763C949.755 332.715 949.826 332.635 949.869 332.532Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M137.979 50.0883C138.178 49.6112 137.758 49.1501 137.33 49.3647C137.238 49.4125 137.167 49.492 137.124 49.5953C136.932 50.0724 137.345 50.5335 137.772 50.3189C137.865 50.2712 137.936 50.1917 137.979 50.0883Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M1.37284 61.6144C1.56565 61.1373 1.15146 60.6761 0.722978 60.8908C0.630142 60.9385 0.558744 61.018 0.515896 61.1214C0.323081 61.5985 0.737257 62.0596 1.16574 61.8449C1.25857 61.7972 1.32999 61.7177 1.37284 61.6144Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M141.985 101.098C142.177 100.621 141.763 100.16 141.335 100.374C141.242 100.422 141.17 100.502 141.128 100.605C140.935 101.082 141.349 101.543 141.777 101.329C141.87 101.281 141.942 101.201 141.985 101.098Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M430.429 35.0537C430.622 34.5767 430.208 34.1155 429.779 34.3302C429.686 34.3779 429.615 34.4574 429.572 34.5608C429.379 35.0378 429.794 35.499 430.222 35.2843C430.315 35.2366 430.386 35.1571 430.429 35.0537Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M454.051 454.25C454.251 453.773 453.831 453.312 453.403 453.526C453.311 453.574 453.239 453.653 453.197 453.757C453.004 454.234 453.417 454.695 453.845 454.48C453.937 454.433 454.009 454.353 454.051 454.25Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1065.33 439.837C1065.53 439.36 1065.11 438.899 1064.68 439.113C1064.59 439.161 1064.52 439.241 1064.48 439.344C1064.28 439.821 1064.7 440.282 1065.13 440.067C1065.22 440.02 1065.29 439.94 1065.33 439.837Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M763.489 72.7148C763.681 72.2377 763.268 71.7766 762.841 71.9912C762.748 72.0389 762.677 72.1185 762.634 72.2218C762.435 72.6989 762.855 73.16 763.282 72.9454C763.375 72.8977 763.446 72.8181 763.489 72.7148Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M802.036 41.0332C802.235 40.5561 801.815 40.095 801.388 40.3097C801.295 40.3574 801.224 40.4369 801.181 40.5402C800.989 41.0173 801.402 41.4785 801.829 41.2638C801.922 41.2161 801.993 41.1366 802.036 41.0332Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M175.04 247.539C175.233 247.062 174.82 246.6 174.392 246.815C174.3 246.863 174.229 246.942 174.186 247.046C173.986 247.523 174.407 247.984 174.834 247.769C174.926 247.722 174.998 247.642 175.04 247.539Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M412.294 92.3129C412.487 91.8358 412.073 91.3747 411.644 91.5893C411.552 91.637 411.48 91.7165 411.437 91.8199C411.245 92.297 411.659 92.7581 412.087 92.5434C412.18 92.4957 412.251 92.4162 412.294 92.3129Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M854.258 334.095C854.457 333.618 854.037 333.157 853.61 333.371C853.517 333.419 853.446 333.499 853.403 333.602C853.211 334.079 853.624 334.54 854.051 334.325C854.144 334.278 854.215 334.198 854.258 334.095Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1059.6 277.286C1059.8 276.809 1059.38 276.348 1058.96 276.563C1058.86 276.61 1058.79 276.69 1058.75 276.793C1058.56 277.27 1058.97 277.731 1059.4 277.517C1059.49 277.469 1059.56 277.39 1059.6 277.286Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M1071.12 216.208C1071.32 215.731 1070.9 215.27 1070.47 215.485C1070.38 215.532 1070.31 215.612 1070.27 215.715C1070.07 216.192 1070.49 216.654 1070.92 216.439C1071.01 216.391 1071.08 216.312 1071.12 216.208Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M344.991 178.481C344.798 178.004 345.213 177.543 345.641 177.757C345.734 177.805 345.805 177.885 345.848 177.988C346.041 178.465 345.627 178.926 345.198 178.712C345.105 178.664 345.034 178.584 344.991 178.481Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M649.388 397.34C649.195 396.863 649.609 396.402 650.038 396.616C650.13 396.664 650.202 396.744 650.245 396.847C650.438 397.324 650.023 397.785 649.595 397.57C649.502 397.523 649.431 397.443 649.388 397.34Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M288.692 381.379C288.5 380.902 288.913 380.441 289.34 380.655C289.433 380.703 289.504 380.782 289.547 380.886C289.746 381.363 289.326 381.824 288.898 381.609C288.806 381.562 288.735 381.482 288.692 381.379Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M559.69 399.465C559.498 398.988 559.912 398.527 560.34 398.741C560.433 398.789 560.505 398.869 560.547 398.972C560.74 399.449 560.326 399.91 559.898 399.696C559.805 399.648 559.733 399.568 559.69 399.465Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M682.786 414.336C682.594 413.861 683.007 413.401 683.434 413.615C683.527 413.662 683.598 413.742 683.641 413.845C683.84 414.321 683.42 414.788 682.992 414.566C682.9 414.519 682.829 414.44 682.786 414.336Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M248.524 290.102C248.331 289.625 248.745 289.164 249.172 289.379C249.265 289.426 249.336 289.506 249.378 289.609C249.578 290.086 249.158 290.547 248.73 290.333C248.638 290.285 248.567 290.206 248.524 290.102Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M194.429 117.202C194.857 116.98 195.271 117.448 195.079 117.924C195.036 118.027 194.964 118.106 194.872 118.154C194.443 118.368 194.029 117.908 194.222 117.432C194.265 117.329 194.336 117.25 194.429 117.202Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M109.297 277.544C109.105 277.067 109.518 276.606 109.945 276.821C110.038 276.869 110.109 276.948 110.152 277.052C110.351 277.529 109.931 277.99 109.504 277.775C109.411 277.727 109.34 277.648 109.297 277.544Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M877.723 34.1689C877.523 33.6919 877.943 33.2307 878.371 33.4454C878.463 33.4931 878.535 33.5726 878.577 33.676C878.77 34.153 878.357 34.6142 877.929 34.3995C877.837 34.3518 877.765 34.2723 877.723 34.1689Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M987.66 320.267C987.46 319.792 987.88 319.324 988.308 319.546C988.4 319.593 988.472 319.673 988.514 319.776C988.707 320.252 988.294 320.711 987.866 320.497C987.774 320.45 987.702 320.371 987.66 320.267Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1022.75 367.559C1022.55 367.082 1022.98 366.62 1023.4 366.835C1023.5 366.883 1023.57 366.962 1023.61 367.066C1023.8 367.543 1023.39 368.004 1022.96 367.789C1022.87 367.742 1022.8 367.662 1022.75 367.559Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M1109.32 134.922C1109.13 134.445 1109.54 133.983 1109.97 134.198C1110.06 134.246 1110.13 134.325 1110.18 134.429C1110.37 134.906 1109.95 135.367 1109.53 135.152C1109.43 135.105 1109.36 135.025 1109.32 134.922Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1086.83 104.824C1086.64 104.347 1087.06 103.886 1087.48 104.101C1087.58 104.149 1087.65 104.228 1087.69 104.331C1087.88 104.808 1087.47 105.27 1087.04 105.055C1086.95 105.007 1086.88 104.928 1086.83 104.824Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M1020.58 248.601C1020.38 248.124 1020.8 247.663 1021.23 247.877C1021.32 247.925 1021.4 248.005 1021.44 248.108C1021.63 248.585 1021.22 249.046 1020.79 248.832C1020.7 248.784 1020.63 248.704 1020.58 248.601Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M727.631 1.05634C727.438 0.579279 727.851 0.11812 728.279 0.332796C728.371 0.380502 728.443 0.460006 728.485 0.563369C728.685 1.04043 728.264 1.50159 727.837 1.28691C727.745 1.23921 727.673 1.1597 727.631 1.05634Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M705.08 12.1884C704.887 11.7114 705.301 11.2502 705.73 11.4649C705.823 11.5126 705.894 11.5921 705.937 11.6955C706.13 12.1725 705.716 12.6337 705.287 12.419C705.194 12.3713 705.123 12.2918 705.08 12.1884Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M178.922 362.278C178.729 361.801 179.144 361.34 179.572 361.555C179.665 361.602 179.736 361.682 179.779 361.785C179.972 362.262 179.558 362.723 179.129 362.509C179.037 362.461 178.965 362.382 178.922 362.278Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M62.5446 231.603C62.3452 231.127 62.7654 230.659 63.1928 230.881C63.2853 230.928 63.3566 231.008 63.3993 231.111C63.5916 231.587 63.1785 232.047 62.7512 231.832C62.6586 231.785 62.5874 231.706 62.5446 231.603Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M95.9351 209.74C95.7356 209.263 96.1558 208.802 96.5832 209.016C96.6758 209.064 96.747 209.144 96.7897 209.247C96.982 209.724 96.5689 210.185 96.1416 209.971C96.049 209.923 95.9778 209.843 95.9351 209.74Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M263.083 233.887C262.891 233.41 263.304 232.949 263.731 233.163C263.824 233.211 263.895 233.291 263.938 233.394C264.137 233.871 263.717 234.332 263.29 234.117C263.197 234.07 263.126 233.99 263.083 233.887Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M22.4635 120.048C22.2712 119.571 22.6843 119.109 23.1116 119.324C23.2042 119.372 23.2754 119.451 23.3181 119.555C23.5176 120.032 23.0974 120.493 22.67 120.278C22.5775 120.231 22.5062 120.151 22.4635 120.048Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1128.34 181.621C1128.15 181.144 1128.56 180.683 1128.99 180.897C1129.08 180.945 1129.15 181.025 1129.19 181.128C1129.39 181.605 1128.97 182.066 1128.55 181.851C1128.45 181.804 1128.38 181.724 1128.34 181.621Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1171.37 144.96C1170.94 145.175 1170.52 144.714 1170.72 144.237C1170.76 144.134 1170.83 144.054 1170.92 144.006C1171.35 143.792 1171.77 144.253 1171.57 144.73C1171.53 144.833 1171.46 144.913 1171.37 144.96Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M1148.14 110.714C1147.71 110.936 1147.3 110.468 1147.49 109.992C1147.53 109.889 1147.6 109.81 1147.7 109.762C1148.12 109.548 1148.54 110.008 1148.35 110.484C1148.3 110.587 1148.23 110.666 1148.14 110.714Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M774.226 20.6369C774.034 20.1599 774.448 19.6987 774.876 19.9134C774.969 19.9611 775.04 20.0406 775.083 20.144C775.276 20.621 774.862 21.0822 774.433 20.8675C774.341 20.8198 774.269 20.7403 774.226 20.6369Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M201.158 217.693C200.966 217.216 201.379 216.755 201.806 216.97C201.899 217.017 201.97 217.097 202.012 217.2C202.212 217.677 201.792 218.138 201.364 217.924C201.272 217.876 201.201 217.797 201.158 217.693Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M483.358 12.7429C483.165 12.2659 483.579 11.8047 484.008 12.0194C484.101 12.0671 484.172 12.1466 484.215 12.25C484.408 12.727 483.993 13.1882 483.565 12.9735C483.472 12.9258 483.401 12.8463 483.358 12.7429Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M433.113 74.3608C432.92 73.8837 433.333 73.4226 433.761 73.6372C433.853 73.6849 433.925 73.7644 433.967 73.8678C434.167 74.3449 433.746 74.806 433.319 74.5914C433.227 74.5436 433.155 74.4641 433.113 74.3608Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M523.117 74.6624C522.925 74.1854 523.339 73.7242 523.767 73.9389C523.86 73.9866 523.932 74.0661 523.974 74.1694C524.167 74.6465 523.753 75.1077 523.325 74.893C523.232 74.8453 523.16 74.7658 523.117 74.6624Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M366.484 385.437C366.291 384.96 366.705 384.499 367.133 384.714C367.226 384.761 367.298 384.841 367.341 384.944C367.533 385.421 367.119 385.883 366.691 385.668C366.598 385.62 366.526 385.541 366.484 385.437Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1016.29 390.947C1016.09 390.47 1016.51 390.009 1016.94 390.223C1017.03 390.271 1017.1 390.351 1017.14 390.454C1017.33 390.931 1016.92 391.392 1016.49 391.178C1016.4 391.13 1016.33 391.05 1016.29 390.947Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M529.156 411.152C528.963 410.674 529.377 410.213 529.806 410.428C529.899 410.476 529.97 410.555 530.013 410.659C530.206 411.136 529.791 411.597 529.363 411.382C529.27 411.334 529.199 411.255 529.156 411.152Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M594.038 441.962C593.845 441.485 594.259 441.024 594.688 441.238C594.781 441.286 594.852 441.366 594.895 441.469C595.088 441.946 594.674 442.407 594.245 442.193C594.152 442.145 594.081 442.065 594.038 441.962Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M710.06 369.463C709.867 368.985 710.281 368.524 710.71 368.739C710.803 368.787 710.874 368.866 710.917 368.97C711.11 369.447 710.696 369.908 710.267 369.693C710.174 369.645 710.103 369.566 710.06 369.463Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M479.541 416.463C479.348 415.986 479.762 415.525 480.191 415.74C480.284 415.787 480.355 415.867 480.398 415.97C480.591 416.447 480.177 416.908 479.748 416.694C479.655 416.646 479.584 416.567 479.541 416.463Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M518.658 453.648C518.466 453.171 518.879 452.71 519.307 452.924C519.399 452.972 519.47 453.052 519.513 453.155C519.713 453.632 519.292 454.093 518.865 453.879C518.772 453.831 518.701 453.751 518.658 453.648Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M257.204 90.2996C257.012 89.8226 257.425 89.3614 257.852 89.5761C257.945 89.6238 258.016 89.7033 258.059 89.8067C258.258 90.2837 257.838 90.7449 257.411 90.5302C257.318 90.4825 257.247 90.403 257.204 90.2996Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M818.604 376.073C818.412 375.596 818.825 375.135 819.252 375.35C819.345 375.398 819.416 375.477 819.459 375.58C819.658 376.057 819.238 376.519 818.811 376.304C818.718 376.256 818.647 376.177 818.604 376.073Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M785.738 362.532C785.538 362.055 785.958 361.594 786.386 361.809C786.478 361.856 786.55 361.936 786.592 362.039C786.785 362.516 786.372 362.977 785.944 362.763C785.852 362.715 785.78 362.635 785.738 362.532Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M952.83 171.045C952.63 170.569 953.051 170.101 953.478 170.323C953.57 170.37 953.642 170.45 953.684 170.553C953.877 171.029 953.464 171.489 953.036 171.274C952.944 171.227 952.872 171.148 952.83 171.045Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M887.595 62.1538C887.402 61.6767 887.816 61.2156 888.245 61.4302C888.338 61.4779 888.409 61.5575 888.452 61.6608C888.645 62.1379 888.231 62.599 887.802 62.3844C887.709 62.3366 887.638 62.2571 887.595 62.1538Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M837.365 76.4884C837.173 76.0113 837.586 75.5502 838.013 75.7649C838.106 75.8126 838.177 75.8921 838.22 75.9954C838.419 76.4725 837.999 76.9337 837.572 76.719C837.479 76.6713 837.408 76.5918 837.365 76.4884Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M905.92 375.736C905.728 375.259 906.142 374.798 906.57 375.012C906.663 375.06 906.734 375.139 906.777 375.243C906.97 375.72 906.556 376.181 906.127 375.966C906.035 375.919 905.963 375.839 905.92 375.736Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M893.115 99.7108C892.915 99.2337 893.335 98.7725 893.763 98.9872C893.855 99.0349 893.926 99.1144 893.969 99.2178C894.161 99.6949 893.748 100.156 893.321 99.9413C893.228 99.8936 893.157 99.8141 893.115 99.7108Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M947.1 58.4253C946.908 57.9495 947.321 57.4896 947.748 57.7037C947.841 57.7513 947.912 57.8306 947.955 57.9336C948.154 58.4094 947.734 58.8773 947.306 58.6553C947.214 58.6077 947.143 58.5284 947.1 58.4253Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M991.525 259.506C991.332 259.029 991.745 258.568 992.173 258.782C992.265 258.83 992.337 258.909 992.379 259.013C992.579 259.49 992.158 259.951 991.731 259.736C991.639 259.689 991.567 259.609 991.525 259.506Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M966.912 208.783C966.719 208.306 967.133 207.845 967.562 208.06C967.654 208.107 967.726 208.187 967.769 208.29C967.962 208.767 967.547 209.228 967.119 209.014C967.026 208.966 966.955 208.887 966.912 208.783Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1074.97 330.407C1074.77 329.93 1075.19 329.469 1075.62 329.683C1075.71 329.731 1075.78 329.811 1075.82 329.914C1076.02 330.391 1075.6 330.852 1075.17 330.637C1075.08 330.59 1075.01 330.51 1074.97 330.407Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M945.194 288.973C945.001 288.496 945.414 288.035 945.842 288.249C945.934 288.297 946.005 288.376 946.048 288.48C946.248 288.957 945.827 289.418 945.4 289.203C945.307 289.156 945.236 289.076 945.194 288.973Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1104.27 297.132C1104.08 296.655 1104.49 296.194 1104.92 296.408C1105.01 296.456 1105.08 296.536 1105.13 296.639C1105.32 297.116 1104.9 297.577 1104.48 297.362C1104.38 297.315 1104.31 297.235 1104.27 297.132Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M196.94 297.213C196.748 296.736 197.161 296.275 197.588 296.49C197.681 296.537 197.752 296.617 197.795 296.72C197.994 297.197 197.574 297.658 197.147 297.444C197.054 297.396 196.983 297.316 196.94 297.213Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M953.94 380.385C953.747 379.908 954.161 379.447 954.59 379.662C954.683 379.71 954.754 379.789 954.797 379.892C954.99 380.37 954.576 380.831 954.147 380.616C954.054 380.568 953.983 380.489 953.94 380.385Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M725.724 411.152C725.531 410.674 725.946 410.213 726.374 410.428C726.467 410.476 726.538 410.555 726.581 410.659C726.774 411.136 726.36 411.597 725.931 411.382C725.839 411.334 725.767 411.255 725.724 411.152Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M939.329 431.526C939.129 431.049 939.55 430.588 939.977 430.803C940.069 430.851 940.141 430.93 940.183 431.033C940.376 431.511 939.963 431.972 939.535 431.757C939.443 431.709 939.371 431.63 939.329 431.526Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M815.459 110.486C815.659 110.009 815.238 109.548 814.811 109.762C814.718 109.81 814.647 109.889 814.604 109.993C814.412 110.47 814.825 110.931 815.253 110.716C815.345 110.669 815.416 110.589 815.459 110.486Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M930.923 217.791C931.116 217.313 930.702 216.852 930.273 217.067C930.181 217.115 930.109 217.194 930.066 217.298C929.874 217.775 930.288 218.236 930.716 218.021C930.809 217.973 930.88 217.894 930.923 217.791Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M888.352 145.512C888.153 145.035 888.573 144.574 889 144.789C889.093 144.837 889.164 144.916 889.207 145.019C889.399 145.496 888.986 145.958 888.559 145.743C888.466 145.695 888.395 145.616 888.352 145.512Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M881.886 168.901C881.686 168.424 882.107 167.962 882.534 168.177C882.626 168.225 882.698 168.304 882.74 168.408C882.933 168.885 882.52 169.346 882.092 169.131C882 169.084 881.929 169.004 881.886 168.901Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M940.566 108.361C940.367 107.884 940.787 107.422 941.214 107.637C941.307 107.685 941.378 107.764 941.421 107.868C941.613 108.345 941.2 108.806 940.773 108.591C940.68 108.544 940.609 108.464 940.566 108.361Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M819.538 158.339C819.345 157.862 819.759 157.4 820.188 157.615C820.281 157.663 820.352 157.742 820.395 157.846C820.588 158.323 820.174 158.784 819.745 158.569C819.652 158.521 819.581 158.442 819.538 158.339Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M804.927 209.48C804.727 209.003 805.148 208.542 805.575 208.757C805.667 208.804 805.739 208.884 805.781 208.987C805.974 209.464 805.561 209.925 805.133 209.711C805.041 209.663 804.97 209.584 804.927 209.48Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M886.205 310.233C886.005 309.756 886.425 309.295 886.853 309.51C886.945 309.558 887.016 309.637 887.059 309.74C887.252 310.217 886.838 310.679 886.411 310.464C886.319 310.416 886.247 310.337 886.205 310.233Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1131.09 249.937C1130.9 249.46 1131.31 248.999 1131.74 249.214C1131.83 249.261 1131.91 249.341 1131.95 249.444C1132.15 249.921 1131.73 250.382 1131.3 250.168C1131.21 250.12 1131.14 250.04 1131.09 249.937Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M1208.25 82.0285C1207.82 82.2505 1207.41 81.7826 1207.6 81.3069C1207.64 81.2038 1207.72 81.1245 1207.81 81.0769C1208.24 80.8628 1208.65 81.3227 1208.46 81.7985C1208.42 81.9016 1208.34 81.9809 1208.25 82.0285Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M741.195 149.795C741.388 149.318 740.974 148.857 740.545 149.072C740.452 149.119 740.381 149.199 740.338 149.302C740.145 149.779 740.559 150.241 740.988 150.026C741.081 149.978 741.152 149.899 741.195 149.795Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M722.229 289.894C722.428 289.417 722.008 288.956 721.581 289.171C721.488 289.219 721.417 289.298 721.374 289.402C721.182 289.879 721.595 290.34 722.022 290.125C722.115 290.077 722.186 289.998 722.229 289.894Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M680.881 159.602C681.073 159.125 680.66 158.664 680.233 158.878C680.14 158.926 680.069 159.006 680.026 159.109C679.827 159.586 680.247 160.047 680.674 159.832C680.767 159.785 680.838 159.705 680.881 159.602Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M752.649 191.23C752.842 190.753 752.428 190.292 751.999 190.506C751.907 190.554 751.835 190.634 751.792 190.737C751.599 191.214 752.014 191.675 752.442 191.46C752.535 191.413 752.606 191.333 752.649 191.23Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M514.743 253.913C514.942 253.436 514.522 252.975 514.095 253.19C514.002 253.237 513.931 253.317 513.888 253.42C513.696 253.897 514.109 254.358 514.537 254.144C514.629 254.096 514.7 254.016 514.743 253.913Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M676.303 330.407C676.502 329.93 676.082 329.469 675.654 329.683C675.562 329.731 675.491 329.811 675.448 329.914C675.256 330.391 675.669 330.852 676.096 330.637C676.189 330.59 676.26 330.51 676.303 330.407Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M827.072 348.468C827.264 347.991 826.851 347.53 826.423 347.745C826.331 347.793 826.26 347.872 826.217 347.975C826.018 348.453 826.438 348.914 826.865 348.699C826.958 348.651 827.029 348.572 827.072 348.468Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M470.198 151.92C470.397 151.443 469.977 150.982 469.55 151.197C469.457 151.245 469.386 151.324 469.343 151.427C469.151 151.905 469.564 152.366 469.991 152.151C470.084 152.103 470.155 152.024 470.198 151.92Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M465.419 294.31C465.611 293.833 465.198 293.372 464.771 293.587C464.678 293.634 464.607 293.714 464.564 293.817C464.365 294.294 464.785 294.755 465.212 294.541C465.305 294.493 465.376 294.414 465.419 294.31Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M639.117 169.381C639.31 168.905 638.897 168.445 638.469 168.659C638.377 168.707 638.305 168.786 638.263 168.889C638.063 169.365 638.483 169.833 638.911 169.611C639.003 169.563 639.075 169.484 639.117 169.381Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M573.348 362.798C573.54 362.321 573.127 361.859 572.7 362.074C572.607 362.122 572.536 362.201 572.493 362.305C572.294 362.782 572.714 363.243 573.142 363.028C573.234 362.981 573.305 362.901 573.348 362.798Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M757.212 225.568C756.784 225.346 756.37 225.814 756.562 226.29C756.605 226.393 756.677 226.472 756.769 226.52C757.198 226.734 757.612 226.274 757.419 225.798C757.376 225.695 757.305 225.616 757.212 225.568Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M412.645 215.079C412.837 214.602 412.424 214.141 411.996 214.356C411.904 214.403 411.833 214.483 411.79 214.586C411.591 215.063 412.011 215.525 412.438 215.31C412.531 215.262 412.602 215.183 412.645 215.079Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M451.188 183.398C451.387 182.921 450.967 182.459 450.54 182.674C450.447 182.722 450.376 182.801 450.333 182.905C450.141 183.382 450.554 183.843 450.981 183.628C451.074 183.581 451.145 183.501 451.188 183.398Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M720.27 358.574C720.463 358.097 720.049 357.635 719.621 357.85C719.528 357.898 719.456 357.977 719.414 358.081C719.221 358.558 719.635 359.019 720.063 358.804C720.156 358.757 720.228 358.677 720.27 358.574Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M526.871 176.533C526.671 176.056 527.091 175.595 527.519 175.81C527.611 175.858 527.682 175.937 527.725 176.04C527.917 176.518 527.504 176.979 527.077 176.764C526.984 176.716 526.913 176.637 526.871 176.533Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M758.469 277.286C758.276 276.809 758.689 276.348 759.117 276.563C759.209 276.61 759.281 276.69 759.323 276.793C759.523 277.27 759.103 277.731 758.675 277.517C758.583 277.469 758.511 277.39 758.469 277.286Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M735.983 247.189C735.783 246.712 736.203 246.251 736.631 246.466C736.723 246.514 736.795 246.593 736.837 246.696C737.03 247.173 736.617 247.635 736.189 247.42C736.097 247.372 736.025 247.293 735.983 247.189Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M777.487 323.986C777.294 323.509 777.707 323.048 778.135 323.263C778.227 323.31 778.299 323.39 778.341 323.493C778.541 323.97 778.121 324.432 777.693 324.217C777.601 324.169 777.529 324.09 777.487 323.986Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M820.515 287.326C820.086 287.541 819.672 287.079 819.865 286.602C819.908 286.499 819.979 286.419 820.072 286.372C820.5 286.157 820.915 286.618 820.722 287.095C820.679 287.199 820.608 287.278 820.515 287.326Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M797.287 253.078C796.858 253.3 796.444 252.832 796.637 252.357C796.68 252.254 796.751 252.174 796.844 252.127C797.273 251.913 797.687 252.372 797.494 252.848C797.451 252.951 797.38 253.031 797.287 253.078Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M423.374 163.001C423.181 162.524 423.596 162.063 424.024 162.278C424.117 162.326 424.188 162.405 424.231 162.508C424.424 162.985 424.01 163.447 423.581 163.232C423.488 163.184 423.417 163.105 423.374 163.001Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M601.978 313.41C601.778 312.934 602.198 312.466 602.626 312.688C602.718 312.735 602.789 312.815 602.832 312.918C603.024 313.394 602.611 313.854 602.184 313.639C602.091 313.592 602.02 313.513 601.978 313.41Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M536.743 204.519C536.55 204.042 536.964 203.581 537.393 203.795C537.485 203.843 537.557 203.922 537.6 204.026C537.793 204.503 537.378 204.964 536.95 204.749C536.857 204.702 536.786 204.622 536.743 204.519Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M486.521 218.853C486.329 218.376 486.742 217.915 487.169 218.129C487.262 218.177 487.333 218.257 487.376 218.36C487.575 218.837 487.155 219.298 486.727 219.083C486.635 219.036 486.564 218.956 486.521 218.853Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M542.262 242.076C542.063 241.599 542.483 241.138 542.91 241.353C543.003 241.4 543.074 241.48 543.117 241.583C543.309 242.06 542.896 242.521 542.469 242.307C542.376 242.259 542.305 242.18 542.262 242.076Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M436.599 237.975C436.406 237.499 436.82 237.039 437.247 237.253C437.339 237.301 437.411 237.38 437.453 237.483C437.653 237.959 437.233 238.427 436.805 238.205C436.713 238.157 436.641 238.078 436.599 237.975Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M616.06 351.148C615.867 350.671 616.281 350.21 616.709 350.425C616.802 350.472 616.874 350.552 616.917 350.655C617.109 351.132 616.695 351.593 616.267 351.379C616.174 351.331 616.102 351.251 616.06 351.148Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M857.407 224.392C856.978 224.615 856.564 224.147 856.757 223.671C856.8 223.568 856.871 223.488 856.964 223.441C857.393 223.227 857.807 223.687 857.614 224.163C857.571 224.266 857.5 224.345 857.407 224.392Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M218.081 260.287C217.882 259.81 218.302 259.349 218.729 259.564C218.822 259.612 218.893 259.691 218.936 259.795C219.128 260.272 218.715 260.733 218.288 260.518C218.195 260.47 218.124 260.391 218.081 260.287Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M413.696 415.401C413.497 414.924 413.917 414.463 414.345 414.677C414.437 414.725 414.508 414.805 414.551 414.908C414.743 415.385 414.33 415.846 413.903 415.631C413.81 415.584 413.739 415.504 413.696 415.401Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M321.139 410.089C320.94 409.612 321.36 409.151 321.788 409.365C321.88 409.413 321.951 409.492 321.994 409.596C322.186 410.073 321.773 410.534 321.346 410.319C321.253 410.272 321.182 410.192 321.139 410.089Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M409.88 376.091C409.68 375.614 410.1 375.153 410.528 375.368C410.62 375.416 410.692 375.495 410.734 375.598C410.927 376.075 410.513 376.537 410.086 376.322C409.994 376.274 409.922 376.195 409.88 376.091Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M298.39 291.258C298.191 290.781 298.611 290.32 299.038 290.534C299.131 290.582 299.202 290.662 299.245 290.765C299.437 291.242 299.024 291.703 298.597 291.489C298.504 291.441 298.433 291.361 298.39 291.258Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M408.806 289.046C408.606 288.569 409.027 288.107 409.454 288.322C409.546 288.37 409.618 288.449 409.66 288.553C409.853 289.03 409.44 289.491 409.012 289.276C408.92 289.229 408.848 289.149 408.806 289.046Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M344.544 353.137C344.351 352.66 344.765 352.199 345.194 352.413C345.286 352.461 345.358 352.541 345.401 352.644C345.594 353.121 345.179 353.582 344.751 353.368C344.658 353.32 344.587 353.24 344.544 353.137Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M690.32 45.678C690.512 45.2009 690.099 44.7398 689.671 44.9544C689.579 45.0021 689.508 45.0817 689.465 45.185C689.266 45.6621 689.686 46.1232 690.113 45.9086C690.206 45.8608 690.277 45.7813 690.32 45.678Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M661.691 140.233C661.884 139.756 661.471 139.295 661.043 139.51C660.951 139.558 660.879 139.637 660.837 139.74C660.637 140.217 661.058 140.679 661.485 140.464C661.577 140.416 661.649 140.337 661.691 140.233Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M571.137 90.2748C570.945 89.7977 571.358 89.3366 571.785 89.5512C571.878 89.5989 571.949 89.6785 571.992 89.7818C572.191 90.2589 571.771 90.72 571.343 90.5054C571.251 90.4576 571.18 90.3781 571.137 90.2748Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M648.929 94.3334C648.736 93.8564 649.15 93.3952 649.579 93.6099C649.671 93.6576 649.743 93.7371 649.786 93.8405C649.978 94.3175 649.564 94.7787 649.136 94.564C649.043 94.5163 648.972 94.4368 648.929 94.3334Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M696.141 124.297C695.942 123.82 696.362 123.359 696.79 123.574C696.882 123.622 696.953 123.701 696.996 123.805C697.188 124.282 696.775 124.743 696.348 124.528C696.255 124.48 696.184 124.401 696.141 124.297Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M603.588 118.985C603.389 118.508 603.809 118.047 604.236 118.261C604.329 118.309 604.4 118.389 604.443 118.492C604.635 118.969 604.222 119.43 603.795 119.215C603.702 119.168 603.631 119.088 603.588 118.985Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M692.329 84.988C692.129 84.5109 692.549 84.0498 692.977 84.2644C693.069 84.3121 693.14 84.3916 693.183 84.495C693.375 84.9721 692.962 85.4332 692.535 85.2186C692.442 85.1708 692.371 85.0913 692.329 84.988Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M626.993 62.0331C626.8 61.5561 627.214 61.0949 627.642 61.3096C627.735 61.3573 627.807 61.4368 627.85 61.5402C628.042 62.0172 627.628 62.4784 627.2 62.2637C627.107 62.216 627.035 62.1365 626.993 62.0331Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M190.657 334.161C190.464 333.684 190.878 333.223 191.307 333.438C191.399 333.485 191.471 333.565 191.514 333.668C191.707 334.145 191.292 334.606 190.864 334.392C190.771 334.344 190.7 334.264 190.657 334.161Z"
          fill="white"
          fill-opacity="0.8"
        />
        <path
          d="M79.716 82.8629C79.5232 82.3858 79.9374 81.9246 80.3658 82.1393C80.4587 82.187 80.5301 82.2665 80.5729 82.3699C80.7658 82.8469 80.3516 83.3081 79.9231 83.0934C79.8303 83.0457 79.7588 82.9662 79.716 82.8629Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M311.391 29.722C311.198 29.2462 311.611 28.7863 312.039 29.0004C312.131 29.048 312.202 29.1273 312.245 29.2304C312.445 29.7062 312.024 30.174 311.597 29.952C311.505 29.9044 311.433 29.8251 311.391 29.722Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M208.947 10.0107C208.754 9.53367 209.169 9.07253 209.597 9.2872C209.69 9.33491 209.761 9.41441 209.804 9.51778C209.997 9.99483 209.583 10.456 209.154 10.2413C209.061 10.1936 208.99 10.1141 208.947 10.0107Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M208.537 52.0524C208.338 51.5754 208.758 51.1142 209.185 51.3289C209.278 51.3766 209.349 51.4561 209.392 51.5595C209.584 52.0365 209.171 52.4977 208.744 52.283C208.651 52.2353 208.58 52.1558 208.537 52.0524Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M366.941 59.4892C366.742 59.0121 367.162 58.551 367.589 58.7657C367.682 58.8134 367.753 58.8929 367.796 58.9962C367.988 59.4733 367.575 59.9344 367.148 59.7198C367.055 59.6721 366.984 59.5926 366.941 59.4892Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M121.701 154.045C121.508 153.568 121.922 153.107 122.351 153.322C122.444 153.369 122.515 153.449 122.558 153.552C122.751 154.029 122.337 154.49 121.908 154.276C121.815 154.228 121.744 154.148 121.701 154.045Z"
          fill="white"
          fill-opacity="0.1"
        />
        <path
          d="M71.8137 140.699C71.6214 140.222 72.0345 139.761 72.4619 139.975C72.5545 140.023 72.6257 140.103 72.6684 140.206C72.8678 140.683 72.4476 141.144 72.0203 140.929C71.9277 140.882 71.8565 140.802 71.8137 140.699Z"
          fill="white"
          fill-opacity="0.1"
        />
      </svg>

      <h1
        class="heading text-[4rem] md:text-[5rem] lg:text-[7rem] mt-[7rem] text-center font-[bwGrad-regular] tracking-[-1px] leading-[4.5rem] md:leading-[5.5rem] lg:leading-[8.2rem]"
      >
        Learn, Build, and Grow <br />
        with Kodex.
      </h1>
      <p
        class="md:text-lg lg:text-2xl text-sm text-center font-[urbanians-regular] tracking-[.9px] mt-[1rem] lg:mt-[.3rem]"
      >
        Not for Everyone, Only for the Relentless, the Dreamers, and the Doers.
      </p>
      <a
        href="/kodex/registration"
        class="mt-[2rem] lg:mt-[3.7rem] overflow-hidden rounded-full p-[2px] relative inline-block group"
      >
        <span
          class="absolute h-[50%] blur-lg w-full top-1/2 rounded-full bg-gradient-to-r from-white via-white to-black animate-spin-slow"
        ></span>
        <button
          class="relative bg-black hover:opacity-50 transition-all rounded-full px-[2.4rem] tracking-tight text-[1.15rem] py-[.8rem] z-10"
        >
          <svg
            class="absolute top-0 left-0 h-full w-full opacity-60"
            viewBox="0 0 192 88"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M36.2142 70.3239C36.4875 69.7148 35.9004 69.1261 35.2929 69.4001C35.1613 69.461 35.0601 69.5625 34.9993 69.6945C34.7159 70.3036 35.3132 70.8923 35.9206 70.6182C36.0522 70.5573 36.1534 70.4558 36.2142 70.3239Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M20.1396 66.5567C20.4129 65.9477 19.8258 65.3589 19.2183 65.633C19.0867 65.6939 18.9855 65.7954 18.9247 65.9274C18.6413 66.5364 19.2386 67.1252 19.846 66.8511C19.9776 66.7902 20.0789 66.6887 20.1396 66.5567Z"
              fill="white"
              fill-opacity="0.8"
            />
            <path
              d="M51.3363 50.2801C51.6103 49.671 51.0216 49.0823 50.4125 49.3564C50.2806 49.4173 50.1791 49.5188 50.1181 49.6507C49.8441 50.2598 50.4328 50.8486 51.0419 50.5745C51.1738 50.5136 51.2754 50.4121 51.3363 50.2801Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M69.5933 66.5004C69.8666 65.8913 69.2794 65.3026 68.672 65.5766C68.5404 65.6376 68.4392 65.7391 68.3784 65.871C68.0949 66.4801 68.6922 67.0688 69.2997 66.7948C69.4313 66.7338 69.5325 66.6323 69.5933 66.5004Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M58.2599 80.1206C57.9764 79.5115 58.5737 78.9227 59.1812 79.1968C59.3128 79.2577 59.414 79.3592 59.4747 79.4912C59.7481 80.1003 59.1609 80.689 58.5535 80.4149C58.4218 80.354 58.3206 80.2525 58.2599 80.1206Z"
              fill="white"
              fill-opacity="0.8"
            />
            <path
              d="M24.3503 85.546C24.0668 84.937 24.6641 84.3482 25.2715 84.6223C25.4032 84.6832 25.5044 84.7847 25.5651 84.9167C25.8385 85.5257 25.2513 86.1145 24.6439 85.8404C24.5123 85.7795 24.411 85.678 24.3503 85.546Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M38.927 21.494C39.2003 20.885 38.6131 20.2962 38.0057 20.5703C37.8741 20.6312 37.7728 20.7327 37.7121 20.8647C37.4286 21.4737 38.0259 22.0625 38.6334 21.7884C38.765 21.7275 38.8662 21.626 38.927 21.494Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M22.8524 17.7269C23.1257 17.1179 22.5385 16.5291 21.9311 16.8032C21.7995 16.8641 21.6983 16.9656 21.6375 17.0976C21.354 17.7066 21.9514 18.2954 22.5588 18.0213C22.6904 17.9604 22.7916 17.8589 22.8524 17.7269Z"
              fill="white"
              fill-opacity="0.8"
            />
            <path
              d="M60.9726 31.2907C60.6891 30.6817 61.2864 30.0929 61.8939 30.367C62.0255 30.4279 62.1267 30.5294 62.1875 30.6614C62.4608 31.2704 61.8736 31.8592 61.2662 31.5851C61.1346 31.5242 61.0333 31.4227 60.9726 31.2907Z"
              fill="white"
              fill-opacity="0.8"
            />
            <path
              d="M27.063 36.7163C26.7796 36.1072 27.3769 35.5185 27.9843 35.7925C28.1159 35.8534 28.2172 35.9549 28.2779 36.0869C28.5513 36.696 27.9641 37.2847 27.3566 37.0106C27.225 36.9497 27.1238 36.8482 27.063 36.7163Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M1.77413 46.1547C2.04747 45.5456 1.46028 44.9569 0.852841 45.2309C0.721229 45.2918 0.620012 45.3934 0.559268 45.5253C0.275797 46.1344 0.873084 46.7231 1.48052 46.4491C1.61213 46.3881 1.71338 46.2866 1.77413 46.1547Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M7.19966 73.2823C7.47301 72.6732 6.88582 72.0845 6.27838 72.3586C6.14677 72.4195 6.04555 72.521 5.98481 72.6529C5.70133 73.262 6.29862 73.8508 6.90606 73.5767C7.03767 73.5158 7.13892 73.4143 7.19966 73.2823Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M9.42644 29.9026C9.15309 29.2935 9.74027 28.7048 10.3477 28.9789C10.4793 29.0398 10.5806 29.1413 10.6413 29.2732C10.9248 29.8823 10.3275 30.4711 9.72002 30.197C9.58841 30.1361 9.48718 30.0346 9.42644 29.9026Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M16.2085 50.2801C15.9345 49.671 16.5232 49.0823 17.1323 49.3564C17.2642 49.4173 17.3657 49.5188 17.4267 49.6507C17.7007 50.2598 17.112 50.8486 16.5029 50.5745C16.371 50.5136 16.2694 50.4121 16.2085 50.2801Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M132.746 84.1897C133.019 83.5806 132.432 82.9919 131.825 83.266C131.693 83.3269 131.592 83.4284 131.531 83.5603C131.248 84.1694 131.845 84.7582 132.452 84.4841C132.584 84.4232 132.685 84.3217 132.746 84.1897Z"
              fill="white"
              fill-opacity="0.8"
            />
            <path
              d="M146.448 51.6047C146.174 50.9956 146.762 50.4069 147.369 50.681C147.501 50.7419 147.602 50.8434 147.663 50.9753C147.946 51.5844 147.349 52.1731 146.741 51.8991C146.61 51.8382 146.508 51.7366 146.448 51.6047Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M164.081 85.546C163.807 84.937 164.396 84.3482 165.005 84.6223C165.137 84.6832 165.238 84.7847 165.299 84.9167C165.573 85.5257 164.984 86.1145 164.375 85.8404C164.243 85.7795 164.142 85.678 164.081 85.546Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M173.576 62.4876C173.301 61.8785 173.89 61.2898 174.499 61.5638C174.631 61.6247 174.733 61.7262 174.794 61.8582C175.068 62.4673 174.479 63.056 173.87 62.7819C173.738 62.721 173.636 62.6195 173.576 62.4876Z"
              fill="white"
              fill-opacity="0.8"
            />
            <path
              d="M151.565 67.1621C151.838 66.553 151.251 65.9643 150.644 66.2383C150.512 66.2992 150.411 66.4007 150.35 66.5327C150.067 67.1418 150.664 67.7305 151.272 67.4564C151.403 67.3955 151.504 67.294 151.565 67.1621Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M135.459 35.3599C135.732 34.7508 135.145 34.1621 134.537 34.4361C134.406 34.497 134.305 34.5986 134.244 34.7305C133.96 35.3396 134.558 35.9283 135.165 35.6543C135.297 35.5933 135.398 35.4918 135.459 35.3599Z"
              fill="white"
              fill-opacity="0.8"
            />
            <path
              d="M122.519 17.6706C122.792 17.0616 122.205 16.4728 121.598 16.7469C121.466 16.8078 121.365 16.9093 121.304 17.0412C121.02 17.6503 121.618 18.2391 122.225 17.965C122.357 17.9041 122.458 17.8026 122.519 17.6706Z"
              fill="white"
              fill-opacity="0.4"
            />
            <path
              d="M166.794 36.7163C166.52 36.1072 167.108 35.5185 167.717 35.7925C167.849 35.8534 167.951 35.9549 168.012 36.0869C168.286 36.696 167.697 37.2847 167.088 37.0106C166.956 36.9497 166.855 36.8482 166.794 36.7163Z"
              fill="white"
              fill-opacity="0.4"
            />
          </svg>

       Enroll Now
        </button>
      </a>
      <style>
        @keyframes spin-slow {
          0% {
            top: 0%;
            left: 0%;
          }
          25% {
            top: 0%;
            left: 80%;
          }
          50% {
            top: 80%;
            left: 80%;
          }
          75% {
            top: 80%;
            left: 0%;
          }
          100% {
            top: 0%;
            left: 0%;
          }
        }
        .animate-spin-slow {
          animation: spin-slow 5s linear infinite;
        }
        .group:hover .animate-spin-slow {
          animation-play-state: paused;
        }
      </style>
    </div>
    <div
      class="page2 overflow-hidden bg-white text-black mt-[4rem] relative rounded-t-[3rem] py-14"
    >
      <div class="companies md:h-36 md:mb-24">
        <div class="companies overflow-hidden">
          <img src="../Assets/images/companies/nagarro.svg" alt="" />
          <img src="../Assets/images/companies/walmart.svg" alt="" />
          <img src="../Assets/images/companies/rapido.svg" alt="" />
          <img src="../Assets/images/companies/tcs.svg" alt="" />
          <img src="../Assets/images/companies/amazon.svg" alt="" />
          <img src="../Assets/images/companies/nagarro.svg" alt="" />
          <img src="../Assets/images/companies/walmart.svg" alt="" />
          <img src="../Assets/images/companies/rapido.svg" alt="" />
          <img src="../Assets/images/companies/tcs.svg" alt="" />
          <img src="../Assets/images/companies/amazon.svg" alt="" />
        </div>
      </div>
      <div
        class="flex md:flex-row flex-col justify-between pt-1 lg:pl-[3.76rem] lg:pr-[3.7rem] md:pl-[2.5rem] md:pr-[2.7rem] px-[1.5rem] pt-[2.43rem]"
      >
        <h1 class="md:text-2xl tracking-[1.5px] text-3xl font-[urbanians-medium]">
          About
        </h1>
        <div class="w-full mt-3 sm:mt-0 lg:w-[61.2%]">
          <p
            class="md:text-3xl lg:text-[2.7rem] text-xl text-gray-400 font-[urbanians-medium] tracking-[1px] md:leading-[2.5rem] lg:leading-[3.2rem]"
          >
            <span class="text-black font-[urbanians-medium] ">
              Kodex is an exclusive online learning platform by Sheryians,</span
            >
            designed for dedicated learners who want to push their
            <span class="text-black font-[urbanians-medium] ">coding journey to the next level.</span>
            Access is granted only through a qualifying exam, ensuring a
            community of passionate, driven coders.
          </p>
        </div>
      </div>
      <div
        class="flex md:w-[61.8%] w-full  px-[1.5rem] ml-auto items-center ml-auto  gap-2 md:gap-[2.5rem] lg:gap-[5rem] mt-14"
      >
        <div class="metrics">
          <h1
            data-value="580"
            class="md:text-3xl text-3xl lg:text-6xl lg:tracking-[3px] font-[urbanians-medium] mb-1"
          >
            0k+
          </h1>
          <svg 
          class="sm:scale-100 scale-[.7] origin-left"
            width="131"
            height="24"
            viewBox="0 0 131 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17 20H7C4 20 2 18 2 15V9C2 6 4 4 7 4H17C20 4 22 6 22 9V15C22 18 20 20 17 20Z"
              stroke="#717179"
              stroke-width="1.5"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M11.3996 9.49981L13.8996 10.9998C14.7996 11.5998 14.7996 12.4998 13.8996 13.0998L11.3996 14.5998C10.3996 15.1998 9.59961 14.6998 9.59961 13.5998V10.5998C9.59961 9.29981 10.3996 8.89981 11.3996 9.49981Z"
              stroke="#717179"
              stroke-width="1.5"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M34.09 19.26C33.3433 19.26 32.62 19.1433 31.92 18.91C31.2267 18.6767 30.62 18.3433 30.1 17.91C29.58 17.4767 29.2133 16.96 29 16.36L30.31 15.87C30.4567 16.2833 30.7233 16.64 31.11 16.94C31.5033 17.2333 31.96 17.46 32.48 17.62C33.0067 17.78 33.5433 17.86 34.09 17.86C34.73 17.86 35.3267 17.7533 35.88 17.54C36.4333 17.3267 36.88 17.03 37.22 16.65C37.56 16.2633 37.73 15.81 37.73 15.29C37.73 14.7633 37.5533 14.33 37.2 13.99C36.8467 13.65 36.39 13.3833 35.83 13.19C35.2767 12.99 34.6967 12.8367 34.09 12.73C33.1367 12.5633 32.28 12.3367 31.52 12.05C30.7667 11.7633 30.17 11.36 29.73 10.84C29.29 10.3133 29.07 9.62 29.07 8.76C29.07 7.95333 29.3033 7.25333 29.77 6.66C30.2367 6.06667 30.85 5.60667 31.61 5.28C32.3767 4.95333 33.2033 4.79 34.09 4.79C34.8167 4.79 35.5267 4.90333 36.22 5.13C36.9133 5.35667 37.5233 5.68667 38.05 6.12C38.5833 6.55333 38.9667 7.08333 39.2 7.71L37.87 8.19C37.7233 7.77 37.4533 7.41333 37.06 7.12C36.6733 6.82 36.22 6.59333 35.7 6.44C35.18 6.28 34.6433 6.2 34.09 6.2C33.4567 6.19333 32.8633 6.3 32.31 6.52C31.7633 6.73333 31.32 7.03333 30.98 7.42C30.64 7.8 30.47 8.24667 30.47 8.76C30.47 9.36 30.6267 9.82333 30.94 10.15C31.26 10.47 31.6933 10.71 32.24 10.87C32.7867 11.03 33.4033 11.1733 34.09 11.3C34.9833 11.4533 35.8133 11.6933 36.58 12.02C37.3467 12.34 37.9633 12.77 38.43 13.31C38.8967 13.8433 39.13 14.5033 39.13 15.29C39.13 16.09 38.8967 16.79 38.43 17.39C37.9633 17.9833 37.3467 18.4433 36.58 18.77C35.82 19.0967 34.99 19.26 34.09 19.26ZM40.9094 15.17V9H42.3094V14.89C42.3094 15.4367 42.4427 15.9333 42.7094 16.38C42.976 16.8267 43.3327 17.1833 43.7794 17.45C44.2327 17.7167 44.7294 17.85 45.2694 17.85C45.816 17.85 46.3094 17.7167 46.7494 17.45C47.196 17.1833 47.5527 16.8267 47.8194 16.38C48.086 15.9333 48.2194 15.4367 48.2194 14.89V9H49.6194L49.6294 19H48.2294L48.2194 17.6C47.866 18.1067 47.406 18.51 46.8394 18.81C46.2794 19.11 45.666 19.26 44.9994 19.26C44.246 19.26 43.5594 19.0767 42.9394 18.71C42.3194 18.3433 41.826 17.85 41.4594 17.23C41.0927 16.61 40.9094 15.9233 40.9094 15.17ZM56.7186 8.72C57.6919 8.72 58.5753 8.95667 59.3686 9.43C60.1686 9.90333 60.8053 10.54 61.2786 11.34C61.7519 12.1333 61.9886 13.0167 61.9886 13.99C61.9886 14.7167 61.8519 15.4 61.5786 16.04C61.3053 16.6733 60.9253 17.2333 60.4386 17.72C59.9586 18.2 59.3986 18.5767 58.7586 18.85C58.1253 19.1233 57.4453 19.26 56.7186 19.26C55.8919 19.26 55.1786 19.0633 54.5786 18.67C53.9786 18.2767 53.5053 17.7633 53.1586 17.13V19H51.7586V4H53.1586V10.85C53.5053 10.2167 53.9786 9.70333 54.5786 9.31C55.1786 8.91667 55.8919 8.72 56.7186 8.72ZM56.7086 17.89C57.4219 17.89 58.0686 17.7167 58.6486 17.37C59.2286 17.0167 59.6919 16.5433 60.0386 15.95C60.3853 15.3567 60.5586 14.7033 60.5586 13.99C60.5586 13.2567 60.3819 12.5967 60.0286 12.01C59.6753 11.4167 59.2053 10.9467 58.6186 10.6C58.0386 10.2533 57.4019 10.08 56.7086 10.08C56.0019 10.08 55.3886 10.2567 54.8686 10.61C54.3553 10.9633 53.9586 11.4367 53.6786 12.03C53.4053 12.6233 53.2686 13.2767 53.2686 13.99C53.2686 14.7167 53.4086 15.3767 53.6886 15.97C53.9686 16.5567 54.3653 17.0233 54.8786 17.37C55.3986 17.7167 56.0086 17.89 56.7086 17.89ZM66.8017 19.19C66.2551 19.1767 65.7217 19.0767 65.2017 18.89C64.6817 18.7033 64.2284 18.45 63.8417 18.13C63.4551 17.81 63.1784 17.4467 63.0117 17.04L64.2217 16.52C64.3284 16.78 64.5251 17.02 64.8117 17.24C65.1051 17.4533 65.4384 17.6267 65.8117 17.76C66.1851 17.8867 66.5551 17.95 66.9217 17.95C67.3284 17.95 67.7084 17.8833 68.0617 17.75C68.4151 17.6167 68.7017 17.4267 68.9217 17.18C69.1484 16.9267 69.2617 16.63 69.2617 16.29C69.2617 15.9233 69.1417 15.6367 68.9017 15.43C68.6617 15.2167 68.3584 15.05 67.9917 14.93C67.6251 14.8033 67.2484 14.6833 66.8617 14.57C66.1551 14.3767 65.5317 14.1633 64.9917 13.93C64.4517 13.6967 64.0284 13.4 63.7217 13.04C63.4217 12.6733 63.2717 12.2033 63.2717 11.63C63.2717 11.0233 63.4417 10.5 63.7817 10.06C64.1284 9.61333 64.5784 9.27 65.1317 9.03C65.6917 8.78333 66.2884 8.66 66.9217 8.66C67.7351 8.66 68.4751 8.83667 69.1417 9.19C69.8151 9.54333 70.3017 10.0133 70.6017 10.6L69.4717 11.27C69.3517 10.9967 69.1617 10.76 68.9017 10.56C68.6417 10.36 68.3451 10.2033 68.0117 10.09C67.6851 9.97667 67.3517 9.91667 67.0117 9.91C66.5851 9.90333 66.1884 9.96667 65.8217 10.1C65.4551 10.2267 65.1584 10.4167 64.9317 10.67C64.7117 10.9233 64.6017 11.2333 64.6017 11.6C64.6017 11.9667 64.7117 12.2467 64.9317 12.44C65.1517 12.6267 65.4484 12.78 65.8217 12.9C66.2017 13.0133 66.6284 13.1467 67.1017 13.3C67.7084 13.4933 68.2784 13.7133 68.8117 13.96C69.3451 14.2067 69.7751 14.5133 70.1017 14.88C70.4284 15.2467 70.5884 15.71 70.5817 16.27C70.5817 16.87 70.4017 17.3933 70.0417 17.84C69.6817 18.2867 69.2151 18.63 68.6417 18.87C68.0684 19.1033 67.4551 19.21 66.8017 19.19ZM79.3673 16.49L80.6173 17.17C80.164 17.8033 79.5873 18.31 78.8873 18.69C78.194 19.07 77.4407 19.26 76.6273 19.26C75.7073 19.26 74.8673 19.0233 74.1073 18.55C73.354 18.0767 72.7507 17.4433 72.2973 16.65C71.8507 15.85 71.6273 14.9667 71.6273 14C71.6273 13.2667 71.7573 12.5833 72.0173 11.95C72.2773 11.31 72.634 10.75 73.0873 10.27C73.5473 9.78333 74.0807 9.40333 74.6873 9.13C75.294 8.85667 75.9407 8.72 76.6273 8.72C77.4407 8.72 78.194 8.91 78.8873 9.29C79.5873 9.67 80.164 10.18 80.6173 10.82L79.3673 11.49C79.0207 11.05 78.604 10.7133 78.1173 10.48C77.6307 10.24 77.134 10.12 76.6273 10.12C75.954 10.12 75.344 10.3 74.7973 10.66C74.2507 11.0133 73.8173 11.4833 73.4973 12.07C73.184 12.6567 73.0273 13.3 73.0273 14C73.0273 14.7 73.1873 15.3433 73.5073 15.93C73.834 16.5167 74.2707 16.9867 74.8173 17.34C75.364 17.6867 75.9673 17.86 76.6273 17.86C77.174 17.86 77.6873 17.7333 78.1673 17.48C78.6473 17.2267 79.0473 16.8967 79.3673 16.49ZM82.4227 19V9H83.8227V10.38C84.176 9.87333 84.636 9.47 85.2027 9.17C85.7693 8.87 86.386 8.72 87.0527 8.72C87.4393 8.72 87.8127 8.77 88.1727 8.87L87.6027 10.26C87.3227 10.1733 87.0493 10.13 86.7827 10.13C86.2427 10.13 85.746 10.2633 85.2927 10.53C84.846 10.7967 84.4893 11.1533 84.2227 11.6C83.956 12.0467 83.8227 12.5433 83.8227 13.09V19H82.4227ZM89.4344 9H90.8344V19H89.4344V9ZM90.1444 7.25C89.9177 7.25 89.7244 7.17667 89.5644 7.03C89.411 6.87667 89.3344 6.68667 89.3344 6.46C89.3344 6.23333 89.411 6.04667 89.5644 5.9C89.7244 5.74667 89.9177 5.67 90.1444 5.67C90.3644 5.67 90.551 5.74667 90.7044 5.9C90.8644 6.04667 90.9444 6.23333 90.9444 6.46C90.9444 6.68667 90.8677 6.87667 90.7144 7.03C90.561 7.17667 90.371 7.25 90.1444 7.25ZM98.2811 8.72C99.2544 8.72 100.138 8.95667 100.931 9.43C101.731 9.90333 102.368 10.54 102.841 11.34C103.314 12.1333 103.551 13.0167 103.551 13.99C103.551 14.7167 103.414 15.4 103.141 16.04C102.868 16.6733 102.488 17.2333 102.001 17.72C101.521 18.2 100.961 18.5767 100.321 18.85C99.6878 19.1233 99.0078 19.26 98.2811 19.26C97.4544 19.26 96.7411 19.0633 96.1411 18.67C95.5411 18.2767 95.0678 17.7633 94.7211 17.13V19H93.3211V4H94.7211V10.85C95.0678 10.2167 95.5411 9.70333 96.1411 9.31C96.7411 8.91667 97.4544 8.72 98.2811 8.72ZM98.2711 17.89C98.9844 17.89 99.6311 17.7167 100.211 17.37C100.791 17.0167 101.254 16.5433 101.601 15.95C101.948 15.3567 102.121 14.7033 102.121 13.99C102.121 13.2567 101.944 12.5967 101.591 12.01C101.238 11.4167 100.768 10.9467 100.181 10.6C99.6011 10.2533 98.9644 10.08 98.2711 10.08C97.5644 10.08 96.9511 10.2567 96.4311 10.61C95.9178 10.9633 95.5211 11.4367 95.2411 12.03C94.9678 12.6233 94.8311 13.2767 94.8311 13.99C94.8311 14.7167 94.9711 15.3767 95.2511 15.97C95.5311 16.5567 95.9278 17.0233 96.4411 17.37C96.9611 17.7167 97.5711 17.89 98.2711 17.89ZM109.557 19.26C108.637 19.26 107.797 19.0233 107.037 18.55C106.284 18.0767 105.68 17.4433 105.227 16.65C104.78 15.85 104.557 14.9667 104.557 14C104.557 13.2667 104.687 12.5833 104.947 11.95C105.207 11.31 105.564 10.75 106.017 10.27C106.477 9.78333 107.01 9.40333 107.617 9.13C108.224 8.85667 108.87 8.72 109.557 8.72C110.297 8.72 110.977 8.86667 111.597 9.16C112.224 9.45333 112.76 9.86333 113.207 10.39C113.66 10.9167 114 11.5333 114.227 12.24C114.46 12.9467 114.557 13.71 114.517 14.53H106.037C106.117 15.1633 106.317 15.73 106.637 16.23C106.964 16.73 107.377 17.1267 107.877 17.42C108.384 17.7067 108.944 17.8533 109.557 17.86C110.224 17.86 110.824 17.6867 111.357 17.34C111.897 16.9933 112.33 16.5167 112.657 15.91L114.077 16.24C113.677 17.1267 113.074 17.8533 112.267 18.42C111.46 18.98 110.557 19.26 109.557 19.26ZM105.997 13.4H113.107C113.06 12.78 112.87 12.2133 112.537 11.7C112.21 11.18 111.787 10.7667 111.267 10.46C110.747 10.1467 110.177 9.99 109.557 9.99C108.937 9.99 108.37 10.1433 107.857 10.45C107.344 10.75 106.924 11.16 106.597 11.68C106.27 12.1933 106.07 12.7667 105.997 13.4ZM116.231 19V9H117.631V10.38C117.985 9.87333 118.445 9.47 119.011 9.17C119.578 8.87 120.195 8.72 120.861 8.72C121.248 8.72 121.621 8.77 121.981 8.87L121.411 10.26C121.131 10.1733 120.858 10.13 120.591 10.13C120.051 10.13 119.555 10.2633 119.101 10.53C118.655 10.7967 118.298 11.1533 118.031 11.6C117.765 12.0467 117.631 12.5433 117.631 13.09V19H116.231ZM126.333 19.19C125.786 19.1767 125.253 19.0767 124.733 18.89C124.213 18.7033 123.76 18.45 123.373 18.13C122.986 17.81 122.71 17.4467 122.543 17.04L123.753 16.52C123.86 16.78 124.056 17.02 124.343 17.24C124.636 17.4533 124.97 17.6267 125.343 17.76C125.716 17.8867 126.086 17.95 126.453 17.95C126.86 17.95 127.24 17.8833 127.593 17.75C127.946 17.6167 128.233 17.4267 128.453 17.18C128.68 16.9267 128.793 16.63 128.793 16.29C128.793 15.9233 128.673 15.6367 128.433 15.43C128.193 15.2167 127.89 15.05 127.523 14.93C127.156 14.8033 126.78 14.6833 126.393 14.57C125.686 14.3767 125.063 14.1633 124.523 13.93C123.983 13.6967 123.56 13.4 123.253 13.04C122.953 12.6733 122.803 12.2033 122.803 11.63C122.803 11.0233 122.973 10.5 123.313 10.06C123.66 9.61333 124.11 9.27 124.663 9.03C125.223 8.78333 125.82 8.66 126.453 8.66C127.266 8.66 128.006 8.83667 128.673 9.19C129.346 9.54333 129.833 10.0133 130.133 10.6L129.003 11.27C128.883 10.9967 128.693 10.76 128.433 10.56C128.173 10.36 127.876 10.2033 127.543 10.09C127.216 9.97667 126.883 9.91667 126.543 9.91C126.116 9.90333 125.72 9.96667 125.353 10.1C124.986 10.2267 124.69 10.4167 124.463 10.67C124.243 10.9233 124.133 11.2333 124.133 11.6C124.133 11.9667 124.243 12.2467 124.463 12.44C124.683 12.6267 124.98 12.78 125.353 12.9C125.733 13.0133 126.16 13.1467 126.633 13.3C127.24 13.4933 127.81 13.7133 128.343 13.96C128.876 14.2067 129.306 14.5133 129.633 14.88C129.96 15.2467 130.12 15.71 130.113 16.27C130.113 16.87 129.933 17.3933 129.573 17.84C129.213 18.2867 128.746 18.63 128.173 18.87C127.6 19.1033 126.986 19.21 126.333 19.19Z"
              fill="#717179"
            />
          </svg>
        </div>
        <div class="metrics">
          <h1
            data-value="25"
            class="md:text-3xl text-3xl lg:text-6xl font-[urbanians-medium] lg:tracking-[3px] mb-1"
          >
            0k+
          </h1>
          <svg 
          class="sm:scale-100 scale-[.7] origin-left"
            width="107"
            height="24"
            viewBox="0 0 107 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.0495 2.52979L4.02953 6.45979C2.09953 7.71979 2.09953 10.5398 4.02953 11.7998L10.0495 15.7298C11.1295 16.4398 12.9095 16.4398 13.9895 15.7298L19.9795 11.7998C21.8995 10.5398 21.8995 7.72979 19.9795 6.46979L13.9895 2.53979C12.9095 1.81979 11.1295 1.81979 10.0495 2.52979Z"
              stroke="#717179"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M5.62914 13.0801L5.61914 17.7701C5.61914 19.0401 6.59914 20.4001 7.79914 20.8001L10.9891 21.8601C11.5391 22.0401 12.4491 22.0401 13.0091 21.8601L16.1991 20.8001C17.3991 20.4001 18.3791 19.0401 18.3791 17.7701V13.1301"
              stroke="#717179"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M21.4004 15V9"
              stroke="#717179"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M34.09 19.26C33.3433 19.26 32.62 19.1433 31.92 18.91C31.2267 18.6767 30.62 18.3433 30.1 17.91C29.58 17.4767 29.2133 16.96 29 16.36L30.31 15.87C30.4567 16.2833 30.7233 16.64 31.11 16.94C31.5033 17.2333 31.96 17.46 32.48 17.62C33.0067 17.78 33.5433 17.86 34.09 17.86C34.73 17.86 35.3267 17.7533 35.88 17.54C36.4333 17.3267 36.88 17.03 37.22 16.65C37.56 16.2633 37.73 15.81 37.73 15.29C37.73 14.7633 37.5533 14.33 37.2 13.99C36.8467 13.65 36.39 13.3833 35.83 13.19C35.2767 12.99 34.6967 12.8367 34.09 12.73C33.1367 12.5633 32.28 12.3367 31.52 12.05C30.7667 11.7633 30.17 11.36 29.73 10.84C29.29 10.3133 29.07 9.62 29.07 8.76C29.07 7.95333 29.3033 7.25333 29.77 6.66C30.2367 6.06667 30.85 5.60667 31.61 5.28C32.3767 4.95333 33.2033 4.79 34.09 4.79C34.8167 4.79 35.5267 4.90333 36.22 5.13C36.9133 5.35667 37.5233 5.68667 38.05 6.12C38.5833 6.55333 38.9667 7.08333 39.2 7.71L37.87 8.19C37.7233 7.77 37.4533 7.41333 37.06 7.12C36.6733 6.82 36.22 6.59333 35.7 6.44C35.18 6.28 34.6433 6.2 34.09 6.2C33.4567 6.19333 32.8633 6.3 32.31 6.52C31.7633 6.73333 31.32 7.03333 30.98 7.42C30.64 7.8 30.47 8.24667 30.47 8.76C30.47 9.36 30.6267 9.82333 30.94 10.15C31.26 10.47 31.6933 10.71 32.24 10.87C32.7867 11.03 33.4033 11.1733 34.09 11.3C34.9833 11.4533 35.8133 11.6933 36.58 12.02C37.3467 12.34 37.9633 12.77 38.43 13.31C38.8967 13.8433 39.13 14.5033 39.13 15.29C39.13 16.09 38.8967 16.79 38.43 17.39C37.9633 17.9833 37.3467 18.4433 36.58 18.77C35.82 19.0967 34.99 19.26 34.09 19.26ZM46.4194 10.4H44.0294L44.0194 19H42.6194L42.6294 10.4H40.8194V9H42.6294L42.6194 5.86H44.0194L44.0294 9H46.4194V10.4ZM47.8039 15.17V9H49.2039V14.89C49.2039 15.4367 49.3372 15.9333 49.6039 16.38C49.8706 16.8267 50.2272 17.1833 50.6739 17.45C51.1272 17.7167 51.6239 17.85 52.1639 17.85C52.7106 17.85 53.2039 17.7167 53.6439 17.45C54.0906 17.1833 54.4472 16.8267 54.7139 16.38C54.9806 15.9333 55.1139 15.4367 55.1139 14.89V9H56.5139L56.5239 19H55.1239L55.1139 17.6C54.7606 18.1067 54.3006 18.51 53.7339 18.81C53.1739 19.11 52.5606 19.26 51.8939 19.26C51.1406 19.26 50.4539 19.0767 49.8339 18.71C49.2139 18.3433 48.7206 17.85 48.3539 17.23C47.9872 16.61 47.8039 15.9233 47.8039 15.17ZM66.6683 4H68.0683V19H66.6683V17.13C66.3216 17.77 65.8449 18.2867 65.2383 18.68C64.6383 19.0667 63.9249 19.26 63.0983 19.26C62.3716 19.26 61.6916 19.1233 61.0583 18.85C60.4249 18.5767 59.8649 18.2 59.3783 17.72C58.8983 17.2333 58.5216 16.6733 58.2483 16.04C57.9749 15.4067 57.8383 14.7267 57.8383 14C57.8383 13.2733 57.9749 12.5933 58.2483 11.96C58.5216 11.32 58.8983 10.76 59.3783 10.28C59.8649 9.79333 60.4249 9.41333 61.0583 9.14C61.6916 8.86667 62.3716 8.73 63.0983 8.73C63.9249 8.73 64.6383 8.92667 65.2383 9.32C65.8449 9.70667 66.3216 10.2167 66.6683 10.85V4ZM63.1083 17.9C63.8216 17.9 64.4349 17.7267 64.9483 17.38C65.4616 17.0267 65.8549 16.5533 66.1283 15.96C66.4083 15.3667 66.5483 14.7133 66.5483 14C66.5483 13.2667 66.4083 12.6067 66.1283 12.02C65.8483 11.4267 65.4516 10.9567 64.9383 10.61C64.4249 10.2633 63.8149 10.09 63.1083 10.09C62.4016 10.09 61.7583 10.2667 61.1783 10.62C60.5983 10.9667 60.1349 11.4367 59.7883 12.03C59.4416 12.6233 59.2683 13.28 59.2683 14C59.2683 14.7267 59.4449 15.3867 59.7983 15.98C60.1516 16.5667 60.6183 17.0333 61.1983 17.38C61.7849 17.7267 62.4216 17.9 63.1083 17.9ZM74.7914 19.26C73.8714 19.26 73.0314 19.0233 72.2714 18.55C71.5181 18.0767 70.9147 17.4433 70.4614 16.65C70.0147 15.85 69.7914 14.9667 69.7914 14C69.7914 13.2667 69.9214 12.5833 70.1814 11.95C70.4414 11.31 70.7981 10.75 71.2514 10.27C71.7114 9.78333 72.2447 9.40333 72.8514 9.13C73.4581 8.85667 74.1047 8.72 74.7914 8.72C75.5314 8.72 76.2114 8.86667 76.8314 9.16C77.4581 9.45333 77.9947 9.86333 78.4414 10.39C78.8947 10.9167 79.2347 11.5333 79.4614 12.24C79.6947 12.9467 79.7914 13.71 79.7514 14.53H71.2714C71.3514 15.1633 71.5514 15.73 71.8714 16.23C72.1981 16.73 72.6114 17.1267 73.1114 17.42C73.6181 17.7067 74.1781 17.8533 74.7914 17.86C75.4581 17.86 76.0581 17.6867 76.5914 17.34C77.1314 16.9933 77.5647 16.5167 77.8914 15.91L79.3114 16.24C78.9114 17.1267 78.3081 17.8533 77.5014 18.42C76.6947 18.98 75.7914 19.26 74.7914 19.26ZM71.2314 13.4H78.3414C78.2947 12.78 78.1047 12.2133 77.7714 11.7C77.4447 11.18 77.0214 10.7667 76.5014 10.46C75.9814 10.1467 75.4114 9.99 74.7914 9.99C74.1714 9.99 73.6047 10.1433 73.0914 10.45C72.5781 10.75 72.1581 11.16 71.8314 11.68C71.5047 12.1933 71.3047 12.7667 71.2314 13.4ZM90.1856 12.81V19H88.7856V13.09C88.7856 12.5433 88.6523 12.0467 88.3856 11.6C88.119 11.1533 87.7623 10.7967 87.3156 10.53C86.869 10.2633 86.3723 10.13 85.8256 10.13C85.2856 10.13 84.789 10.2633 84.3356 10.53C83.889 10.7967 83.5323 11.1533 83.2656 11.6C82.999 12.0467 82.8656 12.5433 82.8656 13.09V19H81.4656V9H82.8656V10.38C83.219 9.87333 83.679 9.47 84.2456 9.17C84.8123 8.87 85.429 8.72 86.0956 8.72C86.849 8.72 87.5356 8.90333 88.1556 9.27C88.7756 9.63667 89.269 10.13 89.6356 10.75C90.0023 11.37 90.1856 12.0567 90.1856 12.81ZM97.2983 10.4H94.9083L94.8983 19H93.4983L93.5083 10.4H91.6983V9H93.5083L93.4983 5.86H94.8983L94.9083 9H97.2983V10.4ZM102.095 19.19C101.548 19.1767 101.015 19.0767 100.495 18.89C99.9747 18.7033 99.5214 18.45 99.1347 18.13C98.748 17.81 98.4714 17.4467 98.3047 17.04L99.5147 16.52C99.6214 16.78 99.818 17.02 100.105 17.24C100.398 17.4533 100.731 17.6267 101.105 17.76C101.478 17.8867 101.848 17.95 102.215 17.95C102.621 17.95 103.001 17.8833 103.355 17.75C103.708 17.6167 103.995 17.4267 104.215 17.18C104.441 16.9267 104.555 16.63 104.555 16.29C104.555 15.9233 104.435 15.6367 104.195 15.43C103.955 15.2167 103.651 15.05 103.285 14.93C102.918 14.8033 102.541 14.6833 102.155 14.57C101.448 14.3767 100.825 14.1633 100.285 13.93C99.7447 13.6967 99.3214 13.4 99.0147 13.04C98.7147 12.6733 98.5647 12.2033 98.5647 11.63C98.5647 11.0233 98.7347 10.5 99.0747 10.06C99.4214 9.61333 99.8714 9.27 100.425 9.03C100.985 8.78333 101.581 8.66 102.215 8.66C103.028 8.66 103.768 8.83667 104.435 9.19C105.108 9.54333 105.595 10.0133 105.895 10.6L104.765 11.27C104.645 10.9967 104.455 10.76 104.195 10.56C103.935 10.36 103.638 10.2033 103.305 10.09C102.978 9.97667 102.645 9.91667 102.305 9.91C101.878 9.90333 101.481 9.96667 101.115 10.1C100.748 10.2267 100.451 10.4167 100.225 10.67C100.005 10.9233 99.8947 11.2333 99.8947 11.6C99.8947 11.9667 100.005 12.2467 100.225 12.44C100.445 12.6267 100.741 12.78 101.115 12.9C101.495 13.0133 101.921 13.1467 102.395 13.3C103.001 13.4933 103.571 13.7133 104.105 13.96C104.638 14.2067 105.068 14.5133 105.395 14.88C105.721 15.2467 105.881 15.71 105.875 16.27C105.875 16.87 105.695 17.3933 105.335 17.84C104.975 18.2867 104.508 18.63 103.935 18.87C103.361 19.1033 102.748 19.21 102.095 19.19Z"
              fill="#717179"
            />
          </svg>
        </div>
        <div class="metrics">
          <h1
            data-value="20"
            class="md:text-3xl text-3xl lg:text-6xl font-[urbanians-medium] lg:tracking-[3px] mb-1"
          >
            0+
          </h1>
          <svg 
          class="sm:scale-100 scale-[.7] origin-left"
            width="120"
            height="24"
            viewBox="0 0 120 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.16055 10.87C9.06055 10.86 8.94055 10.86 8.83055 10.87C6.45055 10.79 4.56055 8.84 4.56055 6.44C4.56055 3.99 6.54055 2 9.00055 2C11.4505 2 13.4405 3.99 13.4405 6.44C13.4305 8.84 11.5405 10.79 9.16055 10.87Z"
              stroke="#717179"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M16.4093 4C18.3493 4 19.9093 5.57 19.9093 7.5C19.9093 9.39 18.4093 10.93 16.5393 11C16.4593 10.99 16.3693 10.99 16.2793 11"
              stroke="#717179"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M4.1607 14.56C1.7407 16.18 1.7407 18.82 4.1607 20.43C6.9107 22.27 11.4207 22.27 14.1707 20.43C16.5907 18.81 16.5907 16.17 14.1707 14.56C11.4307 12.73 6.9207 12.73 4.1607 14.56Z"
              stroke="#717179"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M18.34 20C19.06 19.85 19.74 19.56 20.3 19.13C21.86 17.96 21.86 16.03 20.3 14.86C19.75 14.44 19.08 14.16 18.37 14"
              stroke="#717179"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M29.5 19V5H30.9V19H29.5ZM42.3145 12.81V19H40.9145V13.09C40.9145 12.5433 40.7812 12.0467 40.5145 11.6C40.2479 11.1533 39.8912 10.7967 39.4445 10.53C38.9979 10.2633 38.5012 10.13 37.9545 10.13C37.4145 10.13 36.9179 10.2633 36.4645 10.53C36.0179 10.7967 35.6612 11.1533 35.3945 11.6C35.1279 12.0467 34.9945 12.5433 34.9945 13.09V19H33.5945V9H34.9945V10.38C35.3479 9.87333 35.8079 9.47 36.3745 9.17C36.9412 8.87 37.5579 8.72 38.2245 8.72C38.9779 8.72 39.6645 8.90333 40.2845 9.27C40.9045 9.63667 41.3979 10.13 41.7645 10.75C42.1312 11.37 42.3145 12.0567 42.3145 12.81ZM47.4072 19.19C46.8605 19.1767 46.3272 19.0767 45.8072 18.89C45.2872 18.7033 44.8339 18.45 44.4472 18.13C44.0605 17.81 43.7839 17.4467 43.6172 17.04L44.8272 16.52C44.9339 16.78 45.1305 17.02 45.4172 17.24C45.7105 17.4533 46.0439 17.6267 46.4172 17.76C46.7905 17.8867 47.1605 17.95 47.5272 17.95C47.9339 17.95 48.3139 17.8833 48.6672 17.75C49.0205 17.6167 49.3072 17.4267 49.5272 17.18C49.7539 16.9267 49.8672 16.63 49.8672 16.29C49.8672 15.9233 49.7472 15.6367 49.5072 15.43C49.2672 15.2167 48.9639 15.05 48.5972 14.93C48.2305 14.8033 47.8539 14.6833 47.4672 14.57C46.7605 14.3767 46.1372 14.1633 45.5972 13.93C45.0572 13.6967 44.6339 13.4 44.3272 13.04C44.0272 12.6733 43.8772 12.2033 43.8772 11.63C43.8772 11.0233 44.0472 10.5 44.3872 10.06C44.7339 9.61333 45.1839 9.27 45.7372 9.03C46.2972 8.78333 46.8939 8.66 47.5272 8.66C48.3405 8.66 49.0805 8.83667 49.7472 9.19C50.4205 9.54333 50.9072 10.0133 51.2072 10.6L50.0772 11.27C49.9572 10.9967 49.7672 10.76 49.5072 10.56C49.2472 10.36 48.9505 10.2033 48.6172 10.09C48.2905 9.97667 47.9572 9.91667 47.6172 9.91C47.1905 9.90333 46.7939 9.96667 46.4272 10.1C46.0605 10.2267 45.7639 10.4167 45.5372 10.67C45.3172 10.9233 45.2072 11.2333 45.2072 11.6C45.2072 11.9667 45.3172 12.2467 45.5372 12.44C45.7572 12.6267 46.0539 12.78 46.4272 12.9C46.8072 13.0133 47.2339 13.1467 47.7072 13.3C48.3139 13.4933 48.8839 13.7133 49.4172 13.96C49.9505 14.2067 50.3805 14.5133 50.7072 14.88C51.0339 15.2467 51.1939 15.71 51.1872 16.27C51.1872 16.87 51.0072 17.3933 50.6472 17.84C50.2872 18.2867 49.8205 18.63 49.2472 18.87C48.6739 19.1033 48.0605 19.21 47.4072 19.19ZM58.0209 10.4H55.6309L55.6209 19H54.2209L54.2309 10.4H52.4209V9H54.2309L54.2209 5.86H55.6209L55.6309 9H58.0209V10.4ZM59.8055 19V9H61.2055V10.38C61.5588 9.87333 62.0188 9.47 62.5855 9.17C63.1521 8.87 63.7688 8.72 64.4355 8.72C64.8221 8.72 65.1955 8.77 65.5555 8.87L64.9855 10.26C64.7055 10.1733 64.4321 10.13 64.1655 10.13C63.6255 10.13 63.1288 10.2633 62.6755 10.53C62.2288 10.7967 61.8721 11.1533 61.6055 11.6C61.3388 12.0467 61.2055 12.5433 61.2055 13.09V19H59.8055ZM66.3586 15.17V9H67.7586V14.89C67.7586 15.4367 67.8919 15.9333 68.1586 16.38C68.4253 16.8267 68.7819 17.1833 69.2286 17.45C69.6819 17.7167 70.1786 17.85 70.7186 17.85C71.2653 17.85 71.7586 17.7167 72.1986 17.45C72.6453 17.1833 73.0019 16.8267 73.2686 16.38C73.5353 15.9333 73.6686 15.4367 73.6686 14.89V9H75.0686L75.0786 19H73.6786L73.6686 17.6C73.3153 18.1067 72.8553 18.51 72.2886 18.81C71.7286 19.11 71.1153 19.26 70.4486 19.26C69.6953 19.26 69.0086 19.0767 68.3886 18.71C67.7686 18.3433 67.2753 17.85 66.9086 17.23C66.5419 16.61 66.3586 15.9233 66.3586 15.17ZM84.133 16.49L85.383 17.17C84.9296 17.8033 84.353 18.31 83.653 18.69C82.9596 19.07 82.2063 19.26 81.393 19.26C80.473 19.26 79.633 19.0233 78.873 18.55C78.1196 18.0767 77.5163 17.4433 77.063 16.65C76.6163 15.85 76.393 14.9667 76.393 14C76.393 13.2667 76.523 12.5833 76.783 11.95C77.043 11.31 77.3996 10.75 77.853 10.27C78.313 9.78333 78.8463 9.40333 79.453 9.13C80.0596 8.85667 80.7063 8.72 81.393 8.72C82.2063 8.72 82.9596 8.91 83.653 9.29C84.353 9.67 84.9296 10.18 85.383 10.82L84.133 11.49C83.7863 11.05 83.3696 10.7133 82.883 10.48C82.3963 10.24 81.8996 10.12 81.393 10.12C80.7196 10.12 80.1096 10.3 79.563 10.66C79.0163 11.0133 78.583 11.4833 78.263 12.07C77.9496 12.6567 77.793 13.3 77.793 14C77.793 14.7 77.953 15.3433 78.273 15.93C78.5996 16.5167 79.0363 16.9867 79.583 17.34C80.1296 17.6867 80.733 17.86 81.393 17.86C81.9396 17.86 82.453 17.7333 82.933 17.48C83.413 17.2267 83.813 16.8967 84.133 16.49ZM92.2983 10.4H89.9083L89.8983 19H88.4983L88.5083 10.4H86.6983V9H88.5083L88.4983 5.86H89.8983L89.9083 9H92.2983V10.4ZM98.2094 19.26C97.2894 19.26 96.4494 19.0233 95.6894 18.55C94.936 18.0767 94.3327 17.4433 93.8794 16.65C93.4327 15.85 93.2094 14.9667 93.2094 14C93.2094 13.2667 93.3394 12.5833 93.5994 11.95C93.8594 11.31 94.216 10.75 94.6694 10.27C95.1294 9.78333 95.6627 9.40333 96.2694 9.13C96.876 8.85667 97.5227 8.72 98.2094 8.72C99.1294 8.72 99.966 8.95667 100.719 9.43C101.479 9.90333 102.083 10.54 102.529 11.34C102.983 12.14 103.209 13.0267 103.209 14C103.209 14.7267 103.079 15.4067 102.819 16.04C102.559 16.6733 102.199 17.2333 101.739 17.72C101.286 18.2 100.756 18.5767 100.149 18.85C99.5494 19.1233 98.9027 19.26 98.2094 19.26ZM98.2094 17.86C98.8894 17.86 99.4994 17.6833 100.039 17.33C100.586 16.97 101.016 16.4967 101.329 15.91C101.649 15.3233 101.809 14.6867 101.809 14C101.809 13.3 101.649 12.6567 101.329 12.07C101.009 11.4767 100.576 11.0033 100.029 10.65C99.4894 10.2967 98.8827 10.12 98.2094 10.12C97.5294 10.12 96.916 10.3 96.3694 10.66C95.8294 11.0133 95.3994 11.4833 95.0794 12.07C94.766 12.6567 94.6094 13.3 94.6094 14C94.6094 14.72 94.7727 15.3733 95.0994 15.96C95.426 16.54 95.8627 17.0033 96.4094 17.35C96.956 17.69 97.556 17.86 98.2094 17.86ZM104.903 19V9H106.303V10.38C106.656 9.87333 107.116 9.47 107.683 9.17C108.25 8.87 108.866 8.72 109.533 8.72C109.92 8.72 110.293 8.77 110.653 8.87L110.083 10.26C109.803 10.1733 109.53 10.13 109.263 10.13C108.723 10.13 108.226 10.2633 107.773 10.53C107.326 10.7967 106.97 11.1533 106.703 11.6C106.436 12.0467 106.303 12.5433 106.303 13.09V19H104.903ZM115.005 19.19C114.458 19.1767 113.925 19.0767 113.405 18.89C112.885 18.7033 112.432 18.45 112.045 18.13C111.658 17.81 111.382 17.4467 111.215 17.04L112.425 16.52C112.532 16.78 112.728 17.02 113.015 17.24C113.308 17.4533 113.642 17.6267 114.015 17.76C114.388 17.8867 114.758 17.95 115.125 17.95C115.532 17.95 115.912 17.8833 116.265 17.75C116.618 17.6167 116.905 17.4267 117.125 17.18C117.352 16.9267 117.465 16.63 117.465 16.29C117.465 15.9233 117.345 15.6367 117.105 15.43C116.865 15.2167 116.562 15.05 116.195 14.93C115.828 14.8033 115.452 14.6833 115.065 14.57C114.358 14.3767 113.735 14.1633 113.195 13.93C112.655 13.6967 112.232 13.4 111.925 13.04C111.625 12.6733 111.475 12.2033 111.475 11.63C111.475 11.0233 111.645 10.5 111.985 10.06C112.332 9.61333 112.782 9.27 113.335 9.03C113.895 8.78333 114.492 8.66 115.125 8.66C115.938 8.66 116.678 8.83667 117.345 9.19C118.018 9.54333 118.505 10.0133 118.805 10.6L117.675 11.27C117.555 10.9967 117.365 10.76 117.105 10.56C116.845 10.36 116.548 10.2033 116.215 10.09C115.888 9.97667 115.555 9.91667 115.215 9.91C114.788 9.90333 114.392 9.96667 114.025 10.1C113.658 10.2267 113.362 10.4167 113.135 10.67C112.915 10.9233 112.805 11.2333 112.805 11.6C112.805 11.9667 112.915 12.2467 113.135 12.44C113.355 12.6267 113.652 12.78 114.025 12.9C114.405 13.0133 114.832 13.1467 115.305 13.3C115.912 13.4933 116.482 13.7133 117.015 13.96C117.548 14.2067 117.978 14.5133 118.305 14.88C118.632 15.2467 118.792 15.71 118.785 16.27C118.785 16.87 118.605 17.3933 118.245 17.84C117.885 18.2867 117.418 18.63 116.845 18.87C116.272 19.1033 115.658 19.21 115.005 19.19Z"
              fill="#717179"
            />
          </svg>
        </div>
      </div>
    </div>
    <div class="page3 pb-10 md:pb-20 bg-white rounded-b-[3rem] pt-10 md:pt-28">
      <svg
        class="mx-auto -mb-6 w-full"
        height="166"
        viewBox="0 0 956 166"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M48.8021 60.21L111.362 126H82.2921L23.3021 61.23H22.9621V126H0.35211V0.199998H22.9621V59.7H23.3021L77.1921 0.199998H104.392L48.8021 60.21ZM194.059 83.33V85.88H131.839C132.689 101.52 140.169 110.7 152.239 110.7C162.779 110.7 170.089 104.92 172.299 95.57H194.059C190.999 115.8 175.359 127.7 152.239 127.7C125.719 127.7 110.079 111.21 110.079 83.33C110.079 55.28 125.719 38.96 152.239 38.96C178.419 38.96 194.059 55.28 194.059 83.33ZM152.239 55.79C140.849 55.79 133.539 63.95 132.009 78.23H172.129C170.769 63.95 163.459 55.79 152.239 55.79ZM265.722 40.66H289.692L257.052 165.1H233.082L243.452 126H224.922L202.652 40.66H226.622L246.002 124.47H246.342L265.722 40.66ZM425.438 18.39H367.298V53.75H416.598V71.94H367.298V126H343.838V0.199998H425.438V18.39ZM514.967 83.33V85.88H452.747C453.597 101.52 461.077 110.7 473.147 110.7C483.687 110.7 490.997 104.92 493.207 95.57H514.967C511.907 115.8 496.267 127.7 473.147 127.7C446.627 127.7 430.987 111.21 430.987 83.33C430.987 55.28 446.627 38.96 473.147 38.96C499.327 38.96 514.967 55.28 514.967 83.33ZM473.147 55.79C461.757 55.79 454.447 63.95 452.917 78.23H493.037C491.677 63.95 484.367 55.79 473.147 55.79ZM592.884 40.66H615.324V126H592.884V96.59H592.544C592.544 116.14 581.154 127.7 562.114 127.7C540.694 127.7 528.114 111.21 528.114 83.33C528.114 55.45 540.694 38.96 562.114 38.96C581.154 38.96 592.544 50.52 592.544 69.9H592.884V40.66ZM572.484 109.85C586.084 109.85 594.074 98.97 594.074 83.33C594.074 67.52 586.084 56.64 572.484 56.64C558.884 56.64 550.894 66.67 550.894 83.33C550.894 99.99 558.884 109.85 572.484 109.85ZM691.172 56.13H664.652V97.27C664.652 105.26 669.752 109.85 678.252 109.85C682.332 109.85 686.582 108.83 689.642 107.13V123.79C684.542 126.17 677.572 127.7 671.112 127.7C652.922 127.7 642.212 117.5 642.212 100.16V56.13H629.972V40.66H643.572V12.1H666.012V19.24C666.012 30.12 657.852 37.77 644.252 40.32V40.66H691.172V56.13ZM761.105 40.66H783.545V126H761.105V95.91H760.765C760.765 115.8 750.905 127.7 734.075 127.7C715.715 127.7 704.835 114.61 704.835 92.68V40.66H727.275V91.66C727.275 103.22 733.225 109.85 743.255 109.85C754.475 109.85 761.105 101.35 761.105 86.73V40.66ZM854.246 38.96C859.176 38.96 863.936 39.98 867.336 41.68L860.876 61.74C857.476 59.87 853.226 58.68 848.976 58.68C835.886 58.68 828.066 69.39 828.066 87.41V126H805.626V40.66H828.066V75.85H828.406C828.406 52.56 838.096 38.96 854.246 38.96ZM955.241 83.33V85.88H893.021C893.871 101.52 901.351 110.7 913.421 110.7C923.961 110.7 931.271 104.92 933.481 95.57H955.241C952.181 115.8 936.541 127.7 913.421 127.7C886.901 127.7 871.261 111.21 871.261 83.33C871.261 55.28 886.901 38.96 913.421 38.96C939.601 38.96 955.241 55.28 955.241 83.33ZM913.421 55.79C902.031 55.79 894.721 63.95 893.191 78.23H933.311C931.951 63.95 924.641 55.79 913.421 55.79Z"
          fill="url(#paint0_linear_605_749)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_605_749"
            x1="474"
            y1="24"
            x2="474"
            y2="118.5"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#B7B7BF" />
            <stop offset="1" stop-color="#E1E1E1" stop-opacity="0" />
          </linearGradient>
        </defs>
      </svg>
      <div class="features h-[290vh] px-[1rem] md:px-[3rem] lg:px-[6rem] space-y-10">
        <div
          class="h-[90vh] feature w-full sticky top-10 z-[0] pt-[5.5rem] pl-[4rem]"
        >
          <img
            class="absolute top-0 left-0 h-full object-cover z-[-1] rounded-3xl"
            src="../Assets/images/companies/card1.png"
            alt=""
          />
          <div class="h-full flex flex-col justify-between pb-10 lg:pr-10">
            <h1 class="w-[17rem] lg:text-lg lg:leading-[1.37rem]">
              Get instant guidance and code reviews. Learn from industry
              experts, clear your doubts in real-time, and improve your coding
              efficiency with personalized feedback.
            </h1>
            <h1
              class="md:text-[3rem] text-[2rem] lg:text-[4.7rem] w-[17rem] lg:leading-[4.5rem]"
            >
              AI-Powered Assistance
            </h1>
          </div>
          <div
            class="absolute lg:block hidden bottom-1/2 font-[urbanians-thin] translate-y-1/2 right-24 lg:text-xl w-[14rem] tracking-[1px] lg:leading-7"
          >
            Instant Mentorship Code Reviews Expert Guidance Real-Time Doubt
            Solving Learn with Experts
          </div>
        </div>
        <div
          class="h-[90vh] feature w-full sticky top-10 z-[0] pt-[5.5rem] pl-[4rem]"
        >
          <img
            class="absolute top-0 left-0 h-full object-cover z-[-1] rounded-3xl"
            src="../Assets/images/companies/card2.png"
            alt=""
          />
          <div class="h-full flex flex-col justify-between pb-10 pr-10">
            <h1 class="w-[17rem] lg:text-lg lg:leading-[1.37rem]">
              Practice with real-world problems, sharpen your problem-solving
              skills, and build the confidence to tackle industry-level
              challenges.
            </h1>
         <h1
              class="md:text-[3rem] text-[2rem] lg:text-[4.7rem] w-[17rem] lg:leading-[4.5rem]"
            >
              Interactive Coding Challenges
            </h1>
          </div>
           <div
            class="absolute lg:block hidden bottom-1/2 font-[urbanians-thin] translate-y-1/2 right-24 lg:text-xl w-[14rem] tracking-[1px] lg:leading-7"
          >
            Code Challenges DSA & Full Stack Project-Based Training Hands-on
            Practice Practical Learning.
          </div>
        </div>
        <div class="h-[90vh] w-full sticky top-10 z-[0] pt-[5.5rem] pl-[4rem]">
          <img
            class="absolute top-0 left-0 h-full object-cover z-[-1] rounded-3xl"
            src="../Assets/images/companies/card3.png"
            alt=""
          />
          <div class="h-full flex flex-col justify-between pb-10 pr-10">
            <h1 class="w-[17rem] lg:text-lg lg:leading-[1.37rem]">
              Exclusive access to a handpicked Sheryians community of dedicated
              learners. Collaborate, connect, and grow with equally ambitious
              peers.
            </h1>
         <h1
              class="md:text-[3rem] text-[2rem] lg:text-[4.7rem] w-[17rem] lg:leading-[4.5rem]"
            >
              Elite learning community
            </h1>
          </div>
          <div
            class="absolute lg:block hidden bottom-1/2 font-[urbanians-thin] translate-y-1/2 right-24 lg:text-xl w-[14rem] tracking-[1px] lg:leading-7"
          >
            Coding School learners community developers growth platform coding
            journey
          </div>
        </div>
      </div>
    </div>
    <section class="page4Heading pb-10 pt-[9.5rem] pl-5 md:pl-14">
      <h1 class="font-[bwGrad-regular] text-[3rem] md:text-[7rem] mb-3 leading-none">
        Why Kodex?
      </h1>
      <p class="text-xl md:text-2xl  md:leading-[2rem] lg:mt-0 mt-5 md:mt-10 lg:w-1/2">
        Because it’s your exclusive gateway to structured learning, <br />
        real-world projects, and industry-ready coding skills.
      </p>
    </section>
    <div class="page4Content  flex w-full relative">
      <div
        style="perspective: 1000px"
        class="leftVideo lg:flex hidden h-screen sticky top-0 w-1/2 items-center justify-center"
      >
        <video
          autoplay
          muted
          loop
          class="h-full w-[70%] object-contain"
          src="../Assets/images/companies/9cccea11555caca1079e3c3eacea2b13_720w.mp4"
        ></video>
      </div>
      <div
        class="h-full w-full lg:w-1/2 flex gap-[1.5rem] md:gap-[4.3rem] pl-5 md:pl-14 pr-10 lg:px-10"
      >
        <div
          class="fillinLine h-[180.7vh]  md:h-[170vh] lg:h-[200vh] pt-[9.5rem] sm:pt-[10rem] pb-[17rem] relative"
        >
          <div
            class="h-full w-[2px] bg-gray-400/40 relative flex justify-center"
          >
            <div class="ball absolute h-4 w-4 bg-white rounded-full"></div>
            <div
              class="ball absolute top-[0%] h-4 w-4 bg-white rounded-full"
            ></div>
            <div
              class="ball absolute top-[33.7%] sm:top-[37%] h-4 w-4 bg-white rounded-full"
            ></div>
            <div
              class="ball absolute top-[66.5%] sm:top-[68%] h-4 w-4 bg-white rounded-full"
            ></div>
            <div
              class="ball absolute bottom-0 h-4 w-4 bg-white rounded-full"
            ></div>
            <div id="filler" class="h-0 w-full bg-white"></div>
          </div>
        </div>
        <div class="pt-[8.8rem] pb-[10rem]">
          <div class="content mb-[8rem]">
            <h1 class="text-[1.5rem] md:text-[2.3rem] text-white/50 ">01</h1>
            <h1 class="text-[1.5rem]  md:text-[2.75rem] md:leading-[3.6rem]">
              Comprehensive Resource Library
            </h1>
            <p
              class="text-[1.3rem] md:text-[1.25rem] mt-[2.4rem] font-[urbanians-thin] md:leading-[1.8rem]"
            >
          Access a vast and ever-growing library of learning materials, including tutorials, coding exercises, interview questions, and project guides. The Comprehensive Resource Library ensures learners have all the tools, references, and examples they need to master every concept efficiently and effectively.
            </p>
          </div>
          <div class="content mb-[7rem]">
            <h1 class="text-[1.5rem] md:text-[2.3rem] text-white/50 ">02</h1>
            <h1 class="text-[1.5rem] md:text-[2.75rem] md:leading-[3.6rem]">
              AI + Human Mentorship
            </h1>
            <p
              class="text-[1.3rem] md:text-[1.25rem] mt-[2.4rem] font-[urbanians-thin] md:leading-[1.8rem]"
            >
⁠Experience the perfect blend of AI-driven guidance and personalized human mentorship. Get instant support from intelligent AI tutors while receiving expert insights, feedback, and career advice from experienced mentors who help you stay motivated and achieve your learning goals effectively.
            </p>
          </div>
          <div class="content mb-[6.6rem]">
            <h1 class="text-[1.5rem] md:text-[2.3rem] text-white/50 ">03</h1>
            <h1 class="text-[1.5rem] md:text-[2.75rem] md:leading-[3.6rem]">Future-Ready Skills</h1>
            <p
              class="text-[1.3rem] md:text-[1.25rem] mt-[2.4rem] font-[urbanians-thin] md:leading-[1.8rem]"
            >
    Develop the technical and analytical skills that prepare you for the evolving tech landscape. Learn in-demand tools, frameworks, and problem-solving techniques to stay ahead in your career and confidently tackle real-world challenges in software development and beyond.
            </p>
          </div>
          <div class="content">
            <h1 class="text-[1.5rem] md:text-[2.3rem] text-white/50 ">04</h1>
            <h1 class="text-[1.5rem] md:text-[2.75rem] md:leading-[3.6rem]">
              Collaborative Sheryians Community
            </h1>
            <p
              class="text-[1.3rem] md:text-[1.25rem] mt-[2.4rem] font-[urbanians-thin] md:leading-[1.8rem]"
            >
Join the vibrant Sheryians Community, where learners, mentors, and developers collaborate, share knowledge, and grow together. Engage in discussions, hackathons, and projects that foster teamwork, networking, and continuous learning in a supportive and inspiring environment.
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="pageEvolve flex items-center justify-center">
      <img
        id="whereEvolve"
        class="w-[90%]"
        src="../Assets/images/Frame 1171276311.png"
        alt=""
      />
    </div>
    <div class="flex justify-between px-10 pl-7 md:pl-14 lg:pl-20 py-20">
      <h1 class="md:text-2xl tracking-[1.5px] font-[urbanians-regular]">
        Courses
      </h1>
      <div class="w-[70%] lg:w-[61.2%]">
        <p
          class="md:text-[2rem] text-[1.1rem] lg:text-[2.7rem] font-[urbanians-regular] tracking-[1px] md:leading-[3.2rem]"
        >
          Kodex courses <span class="text-white/50">are designed to take you from</span> coding fundamentals <span class="text-white/50">to
          advanced development skills.</span>
        </p>
      </div>
    </div>


    <div class="sceneWrapper">
      <div class="scene overflow-hidden">
        <div class="carousel">
          <div class="carousel__cell">
            <div id="carousel1" class="carousel_item">
              <div
                class="h-24 md:flex md:h-36 inner-box -top-1/2 md:-top-[80%] left-0 md:-left-[10%] overflow-hidden absolute aspect-square bg-grad inner-shadow rounded-[2rem] p-3 z-[222]"
              >
                <img
                  class="h-full w-full inner-shadow relative z-[-1] object-center object-cover mix-blend-screen ml-1"
                  src="../Assets/images/ec8778ef6000aeef8cb6be6afe9cac87d34cd5c7.png"
                  alt=""
                />
                <div
                  class="box bg-grad-visible inner-shadow rounded-[2rem] h-full w-full absolute top-0 left-0"
                ></div>
              </div>
              <h1
                class="lg:text-[7rem] md:text-[4rem] sm:text-[3rem] text-[2rem] text-center font-[bwGrad-regular]"
              >
                <span class="text-[2rem] mr-4 font-[bwGrad-regular]">01</span>
                Stack Mern
              </h1>
              <div
                class="h-24 md:flex md:h-36 inner-box -bottom-[60%] md:-bottom-[90%] -right-[10%] overflow-hidden absolute aspect-square bg-grad inner-shadow rounded-[2rem] p-3 z-[9999999]"
              >
                <img
                  class="h-full w-full inner-shadow relative z-[-1] object-center object-cover mix-blend-screen "
                  src="../Assets/images/6402eef4cba22d3502c79843cbbfeb42dd760413.png"
                  alt=""
                />
                <div
                  class="box bg-grad-visible inner-shadow rounded-[2rem] h-full w-full absolute top-0 left-0"
                ></div>
              </div>
            </div>
          </div>
          <div class="carousel__cell">
            <div id="carousel2" class="carousel_item">
              <div
                class="h-24 md:h-36 md:flex inner-box opacity-0 -bottom-[60%] md:-bottom-[90%] left-0 md:-left-[10%] overflow-hidden absolute aspect-square bg-grad inner-shadow rounded-[2rem] z-[222]"
              >
                <img
                  class="h-full w-full inner-shadow relative z-[-1] object-center object-cover mix-blend-screen"
                  src="../Assets/images/539122c67ca95a35a13514be149c3a1a62b0e6d7.png"
                  alt=""
                />
                <div
                  class="bg-grad-visible inner-shadow rounded-[2rem] h-full w-full absolute top-0 left-0"
                ></div>
              </div>
              <h1
                class="lg:text-[7rem] md:text-[4rem] sm:text-[3rem] text-[2rem] text-center font-[bwGrad-regular]"
              >
                <span class="text-[2rem] mr-4 font-[bwGrad-regular]">02</span>
                Stack Mern Mystery
              </h1>
              <div
                class="h-24 md:h-36 md:flex inner-box opacity-0 -top-[60%] md:-top-[80%] -right-[10%] overflow-hidden absolute aspect-square bg-grad inner-shadow rounded-[2rem] z-[9999999]"
              >
                <img
                  class="h-full w-full inner-shadow relative z-[-1] object-center object-cover mix-blend-lighten rounded-[2rem]"
                  src="../Assets/images/3c8fd55bb4b0f9cc728a47c05d98124bbe60d267.png"
                  alt=""
                />
                <div
                  class="bg-grad-visible inner-shadow rounded-[2rem] h-full w-full absolute top-0 left-0"
                ></div>
              </div>
            </div>
          </div>
          <div class="carousel__cell">
            <div id="carousel3" class="carousel_item">
              <div
                class="h-24 md:h-36 md:flex inner-box opacity-0 bottom-[80%] -left-[4%] overflow-hidden absolute aspect-square bg-grad inner-shadow rounded-[2rem] p-3 z-[222]"
              >
                <img
                  class="h-full w-full inner-shadow relative z-[-1] object-center object-cover mix-blend-screen ml-1"
                  src="../Assets/images/fd68f1aa6e81640dd63329c165665dd362b67820.png"
                  alt=""
                />
                <div
                  class="bg-grad-visible inner-shadow rounded-[2rem] h-full w-full absolute top-0 left-0"
                ></div>
              </div>
              <h1
                class="lg:text-[7rem] md:text-[4rem] text-[2rem] text-center font-[bwGrad-regular]"
              >
                <span class="text-[2rem] mr-4 font-[bwGrad-regular]">03</span>
                Full Stack Mern
              </h1>
              <div
                class="h-24 md:flex md:h-36 inner-box opacity-0 top-[90%] -right-[10%] overflow-hidden absolute aspect-square bg-grad inner-shadow rounded-[2rem] z-[222]"
              >
                <img
                  class="h-full w-full inner-shadow relative z-[-1] object-center object-cover mix-blend-lighten rounded-[2rem]"
                  src="../Assets/images/bcdcd6a0d449081372aa21fc86ff389f7143b74e.png"
                  alt=""
                />
                <div
                  class="bg-grad-visible inner-shadow rounded-[2rem] h-full w-full absolute top-0 left-0"
                ></div>
              </div>
            </div>
          </div>
          <div class="carousel__cell">
            <div id="carousel4" class="carousel_item">
              <div
                class="h-24 md:flex md:h-36 inner-box opacity-0 -bottom-[64%] md:-bottom-[90%]  md:-left-[10%] left-0 overflow-hidden absolute aspect-square bg-grad inner-shadow rounded-[2rem] p-2 z-[222]"
              >
                <img
                  class="h-full w-full inner-shadow relative z-[-1] object-center object-cover mix-blend-lighten"
                  src="../Assets/images/32bb671236a3482556fb37f3c998eb800ecbf504.png"
                  alt=""
                />
                <div
                  class="bg-grad-visible inner-shadow rounded-[2rem] h-full w-full absolute top-0 left-0"
                ></div>
              </div>
              <h1
                class="lg:text-[7rem] md:text-[4rem] sm:text-[3rem] text-[2rem] text-center font-[bwGrad-regular]"
              >
                <span class="text-[2rem] mr-4 font-[bwGrad-regular]">04</span>
                Full Mystery
              </h1>
              <div
                class="h-24 md:h-36 md:flex inner-box opacity-0 -top-[64%] lg:-top-[80%] md:-right-[10%] right-[-3.5rem] md:flex overflow-hidden absolute aspect-square bg-grad inner-shadow rounded-[2rem] z-[222]"
              >
                <img
                  class="h-full w-full inner-shadow relative z-[-1] object-center object-cover mix-blend-lighten rounded-[2rem]"
                  src="../Assets/images/d28bd0a6fa6c169e002ab514eaba3c1b21ccafd8.png"
                  alt=""
                />
                <div
                  class="bg-grad-visible inner-shadow rounded-[2rem] h-full w-full absolute top-0 left-0"
                ></div>
              </div>
            </div>
          </div>
          <div class="carousel__cell">
            <div id="carousel5" class="carousel_item">
              <div
                class="h-24 md:h-36 md:flex inner-box opacity-0 -top-[60%] lg:-top-[80%]  md:-left-[10%] overflow-hidden absolute aspect-square bg-grad inner-shadow rounded-[2rem] p-1 z-[222]"
              >
                <img
                  class="h-full w-full inner-shadow rotate-[10deg] relative z-[-1] object-left object-cover mix-blend-lighten ml-1"
                  src="../Assets/images/ed24c72747c299f332856ff008cfb32a3daf6a84.png"
                  alt=""
                />
                <div
                  class="bg-grad-visible inner-shadow rounded-[2rem] h-full w-full absolute top-0 left-0"
                ></div>
              </div>
              <h1
                class="lg:text-[7rem] md:text-[4rem] sm:text-[3rem] text-[2rem] text-center font-[bwGrad-regular]"
              >
                <span class="text-[2rem] mr-4 font-[bwGrad-regular]">05</span>
                Full Stack Mern
              </h1>
              <div
                class="h-24 md:h-36 md:flex inner-box opacity-0 -bottom-[60%] lg:-bottom-[90%] -right-[10%] overflow-hidden absolute aspect-square bg-grad inner-shadow rounded-[2rem] p-3 z-[222]"
              >
                <img
                  class="h-full w-full inner-shadow relative z-[-1] object-center object-cover mix-blend-lighten"
                  src="../Assets/images/8c0878a83b00750a9ecc4244633a7175c399a4f3.png"
                  alt=""
                />
                <div
                  class="bg-grad-visible inner-shadow rounded-[2rem] h-full w-full absolute top-0 left-0"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <section class="page7">
      <div class="notforAll">
        <div class="scroller">
          <div class="content">
            <div class="circle"></div>
            Not for
            <div class="imgeShit">
              <div class="img1"></div>
              <div class="img2"></div>
            </div>
            everyone
          </div>
          <div class="content">
            <div class="circle"></div>
            Not for
            <div class="imgeShit">
              <div class="img1"></div>
              <div class="img2"></div>
            </div>
            ordinary
          </div>
          <div class="content">
            <div class="circle"></div>
            Not for
            <div class="imgeShit">
              <div class="img1"></div>
              <div class="img2"></div>
            </div>
            everyone
          </div>
          <div class="content">
            <div class="circle"></div>
            Not for
            <div class="imgeShit">
              <div class="img1"></div>
              <div class="img2"></div>
            </div>
            ordinary
          </div>
        </div>
      </div>
    </section>

    <section class="page8 relative z-0">
      <div class="cta relative overflow-hidden">
        <div class="stuff relative z-[0]">
          <svg
            class="top-0 left-0 absolute z-[-1]"
            width="1440"
            height="720"
            viewBox="0 0 1440 720"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M764.01 -373.708L-550.192 385.047M807.817 -348.416L-506.386 410.339M851.623 -323.124L-462.579 435.631M895.43 -297.833L-418.772 460.922M939.237 -272.541L-374.965 486.214M983.044 -247.249L-331.159 511.506M1026.85 -221.957L-287.352 536.798M1070.66 -196.665L-243.545 562.09M1114.46 -171.373L-199.739 587.382M1158.27 -146.082L-155.932 612.673M1202.08 -120.79L-112.125 637.965M1245.88 -95.498L-68.3183 663.257M1289.69 -70.2061L-24.5115 688.549M1333.5 -44.9143L19.2952 713.841M1377.3 -19.6225L63.102 739.133M1421.11 5.66938L106.909 764.424M1464.92 30.9612L150.715 789.716M1508.72 56.2531L194.522 815.008M1552.53 81.5449L238.329 840.3M1596.34 106.837L282.136 865.592M1640.14 132.129L325.942 890.884M1683.95 157.42L369.749 916.175M1727.76 182.712L413.556 941.467M1771.56 208.004L457.363 966.759M1815.37 233.296L501.169 992.051M1859.18 258.588L544.976 1017.34M1902.99 283.88L588.783 1042.63M1946.79 309.171L632.59 1067.93M1990.6 334.463L676.396 1093.22M676.396 -373.708L1990.6 385.047M632.59 -348.416L1946.79 410.339M588.783 -323.124L1902.99 435.631M544.976 -297.833L1859.18 460.922M501.169 -272.541L1815.37 486.214M457.363 -247.249L1771.56 511.506M413.556 -221.957L1727.76 536.798M369.749 -196.665L1683.95 562.09M325.942 -171.373L1640.14 587.382M282.136 -146.082L1596.34 612.673M238.329 -120.79L1552.53 637.965M194.522 -95.498L1508.72 663.257M150.715 -70.2061L1464.92 688.549M106.909 -44.9143L1421.11 713.841M63.102 -19.6225L1377.3 739.133M19.2952 5.66938L1333.5 764.424M-24.5115 30.9612L1289.69 789.716M-68.3183 56.2531L1245.88 815.008M-112.125 81.5449L1202.08 840.3M-155.932 106.837L1158.27 865.592M-199.739 132.129L1114.46 890.884M-243.545 157.42L1070.66 916.175M-287.352 182.712L1026.85 941.467M-331.159 208.004L983.044 966.759M-374.965 233.296L939.237 992.051M-418.772 258.588L895.43 1017.34M-462.579 283.88L851.623 1042.63M-506.386 309.171L807.817 1067.93M-550.192 334.463L764.01 1093.22M720.203 -399L-593.999 359.755L720.203 1118.51L2034.41 359.755L720.203 -399Z"
              stroke="url(#paint0_linear_605_1106)"
              stroke-width="0.337224"
            />
            <defs>
              <linearGradient
                id="paint0_linear_605_1106"
                x1="231.5"
                y1="281"
                x2="334.376"
                y2="479.105"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="white" />
                <stop offset="1" stop-color="#828282" />
              </linearGradient>
            </defs>
          </svg>
          <div class="left">
            <div class="">
              <h5 class="md:text-lg  text-black/60 mb-2">STAY UPDATED</h5>
              <h2 class="md:text-[2.7rem] sm:leading-[3.2rem] text-[1.6rem] font-[bwGrad-regular]">
                “Be the first to know when<br />we launch. Join the waitlist<br />and
                secure your spot early.”
              </h2>
              <p class="mt-3">
                Sign up to receive exclusive updates, sneak peeks, and be the
                first to access our launch.
              </p>

              <div class="wrapper">
                <form class="form-container">
                  <!-- Email Input Field -->
                  <input
                    type="email"
                    placeholder="<EMAIL>"
                    aria-label="Email address for subscription"
                    required
                    class="email-input"
                  />

                  <!-- Subscribe Button -->
                  <button type="submit" class="subscribe-button">
                    Subscribe
                  </button>
                </form>

                <!-- Small descriptive text -->
                <div class=" mt-4 text-lg  text-black/60">
                  <p>Only updates. No spam.</p>
                </div>
              </div>
            </div>
          </div>
          <div class="right">
            <div class="rightVDo">
              <video autoplay muted loop playsinline class="bg-video">
                <source
                  src="../Assets/images/10833ca0d96bfddcb67865bb6b32d2e6.mp4"
                  type="video/mp4"
                />
              </video>
            </div>
          </div>
        </div>
      </div>
    </section>
    <footer class="w-full overflow-hidden ">
      <div class="footer-content">
        <div class="absolute top-20 right-20">
          <svg
            width="52"
            height="60"
            viewBox="0 0 52 60"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M18.6058 1.03939C18.6058 1.929 18.4125 2.00635 16.0147 2.23842C10.1365 2.8186 8.86027 3.1667 6.34653 5.06194C4.21953 6.64776 3.87147 7.1119 3.29138 9.00714C2.94332 10.1675 2.16987 12.6816 1.5511 14.5768L0.468262 17.9805L2.71129 22.2738C3.94882 24.6332 5.22502 26.7219 5.49573 26.8766C5.80511 27.0313 7.73876 27.7662 9.78842 28.5784L13.5397 30.0095L17.9097 28.7718C20.3074 28.0756 23.2079 27.3407 24.3681 27.1473C27.0752 26.7219 27.4232 26.1804 25.8763 24.5945C25.2576 23.937 24.5228 22.97 24.2521 22.5059C24.02 22.0418 23.0145 21.0748 22.009 20.3786C20.2688 19.1409 18.7605 18.7541 15.28 18.7154C14.0811 18.6767 13.3463 18.3673 12.0701 17.2843C11.1806 16.5108 10.5619 15.7759 10.6779 15.6598C10.7939 15.5051 11.5674 15.4664 12.3795 15.5438C13.2303 15.5825 14.2358 15.4278 14.6999 15.157C15.3186 14.7315 16.0147 14.7315 18.2191 15.1183C20.6168 15.5438 21.0422 15.5051 21.5836 14.9636C22.3184 14.2674 22.1251 14.074 25.3349 18.8701C27.8486 22.6993 29.2022 24.7106 31.3292 27.8048C31.948 28.6558 32.9921 29.6614 33.7269 29.9708C34.8484 30.5123 35.1578 30.551 36.2793 30.0869C37.1688 29.7001 37.8649 29.6614 38.7931 29.9322C41.1908 30.6284 43.0857 30.435 46.1796 29.0812C48.616 28.0369 49.1574 27.6501 49.1574 26.9539C49.1574 26.1804 48.732 25.7549 46.2569 23.7823C45.5608 23.2021 40.1079 18.522 39.2185 17.7098C38.5997 17.1683 38.4063 16.6655 38.561 16.0466C38.8317 15.157 39.2958 14.9249 39.6825 15.5825C40.0306 16.124 41.5002 16.0466 43.0471 15.4278C44.0912 14.9636 44.5553 14.9636 45.2901 15.3117C46.3343 15.8532 46.2956 15.5825 45.4835 18.406C44.71 21.0748 44.826 21.5389 46.4116 22.0804C47.5331 22.4672 47.6105 22.4286 48.3452 20.8814C49.9695 17.4004 50.2015 16.5108 50.0468 15.1183C49.8535 13.6099 49.4281 13.1071 46.1409 10.0515C45.0194 9.04582 44.1299 8.11754 44.1299 8.0015C44.1299 7.92415 44.594 7.1119 45.1354 6.26098C45.6768 5.37137 46.0249 4.59781 45.9089 4.44309C45.7155 4.2497 41.0747 4.90723 39.4118 5.37137C38.9477 5.52609 38.6384 5.1393 38.2516 3.90159C37.7489 2.43181 37.5555 2.23842 36.55 2.2771C35.9313 2.31578 34.8097 2.35446 34.075 2.39314C32.9535 2.43181 32.4894 2.74124 31.4065 4.21102C29.821 6.29965 29.3569 6.26098 25.915 3.94027C24.6775 3.12803 23.5173 2.43181 23.3626 2.43181C23.2079 2.43181 22.3571 1.89032 21.545 1.27146C19.65 -0.159639 18.6058 -0.236996 18.6058 1.03939Z"
              fill="white"
            />
            <path
              d="M47.9603 31.5177C44.1704 35.9657 43.9383 36.3911 44.0543 38.7118C44.1704 40.4137 44.3637 40.9552 45.2145 41.7288C45.7946 42.2316 46.4134 42.657 46.5681 42.657C46.7228 42.657 47.3802 43.0438 48.0376 43.508C49.2365 44.3976 49.2365 44.3976 49.8166 43.624C51.2862 41.6901 51.4795 40.6071 51.0928 36.043C50.8994 33.6063 50.7061 31.1696 50.7061 30.5507C50.7061 28.9649 49.8939 29.2743 47.9603 31.5177Z"
              fill="white"
            />
            <path
              d="M28.0007 32.5643C27.8073 32.6804 27.0725 33.57 26.3377 34.5369C25.603 35.5039 24.9068 36.3548 24.7908 36.4709C24.6748 36.5869 24.1334 37.3218 23.5146 38.0954C22.6638 39.2557 22.4705 39.9132 22.4705 41.6538C22.4705 43.7037 22.5865 43.9358 24.4814 46.0244C27.7686 49.7376 27.6913 49.6602 28.9288 49.1187C29.5089 48.8479 30.9785 48.0357 32.1387 47.3008C33.2989 46.5659 34.7298 45.6763 35.2712 45.3669C40.0279 42.6594 40.1439 42.5434 40.8401 40.4161L41.4975 38.3274L39.9506 36.5482C38.6357 34.9624 38.0943 34.653 35.9673 34.1115C31.0558 32.8738 28.3101 32.3323 28.0007 32.5643Z"
              fill="white"
            />
            <path
              d="M39.679 46.1393C38.2095 47.1836 36.3532 48.3827 35.6184 48.8081C34.8836 49.2336 33.7621 50.0458 33.1046 50.5873L31.9445 51.5543L32.8339 54.0297C33.298 55.4221 33.6847 56.8919 33.6847 57.3174C33.6847 58.0136 33.9168 58.0909 36.1211 58.0909C37.4747 58.0909 39.6017 58.439 41.0326 58.9032L43.5463 59.6768L46.5628 56.5825C49.9273 53.1014 49.8113 53.6816 48.4964 48.5374C48.1097 47.0289 47.607 45.9846 47.2589 45.8686C46.9108 45.7525 45.944 45.3271 45.0546 44.9403C44.2037 44.5148 43.2369 44.2054 42.9275 44.2441C42.6182 44.2441 41.1873 45.095 39.679 46.1393Z"
              fill="white"
            />
          </svg>
        </div>
        <div class="content-grid">
          <div class="footer-column">
            <a href="/aboutus">About Us</a>
            <a href="mailto:<EMAIL>">Support</a>
            <a target="_blank" href="/terms-and-conditions/Terms_and_Conditions.pdf">Privacy Policy</a>
            <a target="_blank" href="/terms-and-conditions/Terms_and_Conditions.pdf">Terms & Conditions</a>
            <!-- <a target="_blank" href="/Pricing&Refund-Policy/Pricing-and-Refund_policy.pdf">Submit Projects</a> -->
          </div>

          <div class="footer-column">
            <a target="_blank" href="https://discord.gg/D23JkFqrgz">Discord</a>
            <a target="_blank" href="/Pricing&Refund-Policy/Pricing-and-Refund_policy.pdf">Pricing & Refund</a>
            <!-- <a href="#">Hire From Us</a> -->
          </div>

          <div class="footer-column contact-address-col">
            <p class="highlight">Online: 11am - 8pm</p>
            <p class="detail">+91 9993478545</p>

            <p class="highlight">Offline: 11am - 8pm</p>
            <p class="detail">+91 9691778470</p>

            <a href="mailto:<EMAIL>" class="detail"
              ><EMAIL></a
            >

            <p class="detail mt">
              23-B, Indrapuri Sector C.<br />
              MP.462001
            </p>
          </div>
        </div>
      </div>

      <h1
        class="text-transparent font-[bwGrad-medium] right-10 text-[8rem] sm:text-[12rem] md:text-[12rem] lg:text-[21rem] leading-none tracking-wide select-none"
      >
        Kodex
      </h1>
      <h1
        class="absolute bottom-0 font-[bwGrad-medium] right-10 text-[8rem] sm:text-[12rem] md:text-[12rem] lg:text-[21rem] leading-none tracking-wide"
      >
        Kodex
      </h1>
    </footer> 
  </body>
  <script
    src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/ScrollTrigger.min.js"
    integrity="sha512-P2IDYZfqSwjcSjX0BKeNhwRUH8zRPGlgcWl5n6gBLzdi4Y5/0O4zaXrtO4K9TZK6Hn1BenYpKowuCavNandERg=="
    crossorigin="anonymous"
    referrerpolicy="no-referrer"
  ></script>
  <script
    src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js"
    integrity="sha512-NcZdtrT77bJr4STcmsGAESr06BYGE8woZdSdEgqnpyqac7sugNO+Tr4bGwGF3MsnEkGKhU2KL2xh6Ec+BqsaHA=="
    crossorigin="anonymous"
    referrerpolicy="no-referrer"
  ></script>
  <script src="../Assets/js/codex.js"></script>
</html>

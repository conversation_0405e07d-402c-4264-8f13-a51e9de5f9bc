<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="https://cdn.tailwindcss.com"></script>
    <title>Kodex | Form</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />
  </head>
  <style>
    .nav-cta {
      text-decoration: none;
      padding: 0.7rem 2rem;

      border-radius: 999px;
      background: #1f1f1f;
      color: #fff;
      font-size: 0.95rem;
      transition: background 0.2s ease;
      white-space: nowrap;
      box-shadow: inset 0 1px 4px rgba(255, 255, 255, 0.1),
        /* soft inner glow at bottom */ inset 0 1px 4px
          rgba(255, 255, 255, 0.05),
        /* soft inner glow at top */ 0 4px 12px rgba(0, 0, 0, 0.5); /* outer drop shadow */
    }
    .inner-shadow {
      box-shadow: inset 0.05px 0 10px rgba(255, 255, 255, 0.03);
    }
    .bg-grad {
      background: radial-gradient(#141414 70%, #1b1b1b);
    }
    .bg-grad-box {
      background-color: #101010ef;
      border-radius: 40px;
    }
    .bg-grad-hover:hover {
      background: radial-gradient(#1b1b1b 0%, #1b1b1b 100%);
    }
    .bg-grad-input {
      box-shadow: inset 4px 0 10px rgba(214, 213, 213, 0.1);
      background-color: #131313e3;
      font-weight: 100;
    }

    @font-face {
      font-family: "urbanians";
      src: url("../Assets/fonts/Urbanist-Regular.ttf");
    }
    * {
      font-family: "urbanians", sans-serif;
    }

    .fontThin {
      font-weight: 100;
    }
    .HeroVideo {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      z-index: -1;
      filter: saturate(0);
      opacity: 70%;
    }
    .HeroVideoCover {
      position: absolute;
      z-index: -1;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #000000;
      background: radial-gradient(rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 60%);
    }
  </style>
  <body class="bg-black h-screen relative pt-1 pb-10">
    <!-- <div class="fixed top-0 left-0 h-full w-full">
      <video autoplay  src="./cfda4326e42bda5e9a3120f31640c4f9-ezgif.com-video-speed.m4v" class="h-full w-full object-cover grayscale-1 z-[-999999]"></video>
    </div> -->
    <video
      id="heroVdo"
      class="HeroVideo"
      src="../Assets/images/cfda4326e42bda5e9a3120f31640c4f9-ezgif.com-video-speed.m4v"
      autoplay
      loop
      muted
      playsinline
    ></video>
    <div class="HeroVideoCover">
      <div class="t"></div>
      <div class="g"></div>
      <div class="b"></div>
    </div>
    <nav
      class="w-full absolute top-[0%] justify-between mb-10 flex items-center lg:pl-[3.76rem] lg:pr-[3.7rem] md:pl-[2.5rem] md:pr-[2.7rem] px-[1.5rem] pt-[2.43rem]"
    >
      <div class="logo">
        <a href="/">
          <svg
            width="157"
            height="39"
            viewBox="0 0 157 39"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M53.096 14.4763C53.096 15.6171 52.3811 16.5906 50.1147 16.5906C47.6658 16.5906 46.6619 15.4498 46.6619 13.6549H47.9091C47.9091 14.963 48.6697 15.5715 50.1299 15.5715C51.3011 15.5715 51.8487 15.2977 51.8487 14.5219C51.8487 13.5332 50.8296 13.3659 49.5975 12.9857C48.1677 12.5293 47.0117 12.0882 47.0117 10.7193C47.0117 9.51762 48.0308 8.72666 49.7648 8.72666C52.1225 8.72666 52.8374 10.0196 52.8374 11.2668H51.5901C51.5901 10.2477 50.9665 9.74578 49.7648 9.74578C48.9283 9.74578 48.259 9.98915 48.259 10.6736C48.259 11.4037 48.7762 11.6319 49.993 12.0274C51.4837 12.5141 53.096 12.8488 53.096 14.4763ZM58.9687 8.72666C60.6723 8.72666 61.7827 9.71536 61.7827 11.8144V16.4385H60.5506V11.9817C60.5506 10.3998 59.927 9.79141 58.6341 9.79141C57.037 9.79141 55.9114 11.0539 55.9114 13.0921V16.4385H54.6641V5.25863H55.9114V10.1869L55.7136 11.0539H56.0635C56.3677 9.68494 57.326 8.72666 58.9687 8.72666ZM63.4952 12.5598C63.4952 10.126 65.0163 8.72666 67.161 8.72666C69.2905 8.72666 70.6899 10.0804 70.6899 12.4076V12.8944H64.7121C64.7881 14.3242 65.4726 15.5563 67.3435 15.5563C68.5908 15.5563 69.4274 14.9478 69.7012 13.7918H70.9484C70.6594 14.963 69.7468 16.5906 67.3435 16.5906C64.636 16.5906 63.4952 14.7349 63.4952 12.5598ZM64.7121 11.9513H69.473C69.473 10.7345 68.6668 9.7762 67.161 9.7762C65.6703 9.7762 64.7121 10.7345 64.7121 11.9513ZM72.6674 16.4385V9.7762C72.6674 9.0613 73.002 8.71145 73.7169 8.74187L77.9151 8.87877V9.94352L73.9146 9.80662V16.4385H72.6674ZM85.4049 8.87877H86.7586L83.4731 16.3624V18.4767C83.4731 19.0547 83.1385 19.4046 82.4236 19.3741L79.5031 19.2372V18.1725L82.2259 18.3094V16.3624L78.9251 8.87877H80.2941L82.667 14.7653V15.3433H83.032V14.7653L85.4049 8.87877ZM90.2919 7.67713C89.6683 7.67713 89.1967 7.22081 89.1967 6.61238C89.1967 6.03438 89.6683 5.56284 90.2919 5.56284C90.9003 5.56284 91.3871 6.03438 91.3871 6.61238C91.3871 7.22081 90.9003 7.67713 90.2919 7.67713ZM87.7669 16.4385V15.3737H89.8964V9.80662L88.3297 9.94352V8.87877L90.079 8.72666C90.7939 8.66582 91.0677 9.0613 91.0677 9.6393V15.3737H93.1971V16.4385H87.7669ZM94.481 14.3394C94.481 13.1378 95.1959 12.3164 96.9604 12.1339L99.9721 11.8449C99.896 10.4607 99.1507 9.7762 97.8426 9.7762C96.7474 9.7762 95.7283 10.2629 95.7283 11.6927H94.5114C94.5114 10.126 95.6979 8.72666 97.8426 8.72666C99.9569 8.72666 101.219 10.126 101.219 12.1491V15.3737H102.33V16.4385H100.991C100.413 16.4385 100.094 16.1191 100.094 15.5563V15.1456L100.292 14.3546H99.9264C99.6374 15.5106 98.8313 16.5906 97.006 16.5906C94.8157 16.5906 94.481 15.1304 94.481 14.3394ZM95.7283 14.1873C95.7283 15.0543 96.2759 15.5258 97.1581 15.5258C98.8769 15.5258 99.9721 14.3394 99.9721 12.7727L97.1733 13.0617C96.215 13.153 95.7283 13.442 95.7283 14.1873ZM107.727 9.79141C106.13 9.79141 105.004 11.0539 105.004 13.0921V16.4385H103.757V8.87877H105.004V10.1869L104.807 11.0539H105.157C105.461 9.68494 106.419 8.72666 108.062 8.72666C109.765 8.72666 110.876 9.71536 110.876 11.8144V16.4385H109.644V11.9817C109.644 10.3998 109.02 9.79141 107.727 9.79141ZM118.87 14.4763C118.87 15.6171 118.155 16.5906 115.889 16.5906C113.44 16.5906 112.436 15.4498 112.436 13.6549H113.683C113.683 14.963 114.444 15.5715 115.904 15.5715C117.075 15.5715 117.623 15.2977 117.623 14.5219C117.623 13.5332 116.604 13.3659 115.372 12.9857C113.942 12.5293 112.786 12.0882 112.786 10.7193C112.786 9.51762 113.805 8.72666 115.539 8.72666C117.897 8.72666 118.612 10.0196 118.612 11.2668H117.364C117.364 10.2477 116.741 9.74578 115.539 9.74578C114.703 9.74578 114.033 9.98915 114.033 10.6736C114.033 11.4037 114.55 11.6319 115.767 12.0274C117.258 12.5141 118.87 12.8488 118.87 14.4763ZM46.814 27.651C46.814 25.4759 48.2286 23.7267 50.6775 23.7267C52.1377 23.7267 54.0086 24.548 54.2064 26.7384H52.9439C52.7309 25.2934 51.7727 24.7914 50.6775 24.7914C48.8826 24.7914 48.0613 26.0995 48.0613 27.651C48.0613 29.2177 48.8826 30.5258 50.6775 30.5258C51.7727 30.5258 52.7309 30.0239 52.9439 28.5789H54.2064C54.0086 30.7692 52.1377 31.5906 50.6775 31.5906C48.2286 31.5906 46.814 29.8414 46.814 27.651ZM55.6225 27.651C55.6225 25.4607 57.2044 23.7267 59.6229 23.7267C62.0262 23.7267 63.5929 25.4607 63.5929 27.651C63.5929 29.8566 62.0262 31.5906 59.6229 31.5906C57.2044 31.5906 55.6225 29.8566 55.6225 27.651ZM56.8698 27.651C56.8698 29.2329 57.8281 30.5258 59.6229 30.5258C61.4026 30.5258 62.3608 29.2329 62.3608 27.651C62.3608 26.0843 61.4026 24.7914 59.6229 24.7914C57.8281 24.7914 56.8698 26.0843 56.8698 27.651ZM73.0869 31.4385H71.8396V30.3281L72.0525 29.4763H71.6875C71.368 30.6475 70.3489 31.5906 68.6301 31.5906C66.4398 31.5906 65.0252 29.9022 65.0252 27.651C65.0252 25.415 66.4398 23.7267 68.6301 23.7267C70.3489 23.7267 71.368 24.6697 71.6875 25.8562H72.0525L71.8396 24.9892V20.2586H73.0869V31.4385ZM71.8396 27.651C71.8396 25.6432 70.5619 24.7914 68.9952 24.7914C67.4285 24.7914 66.2725 25.6432 66.2725 27.651C66.2725 29.674 67.4285 30.5258 68.9952 30.5258C70.5619 30.5258 71.8396 29.674 71.8396 27.651ZM77.3688 22.6771C76.7451 22.6771 76.2736 22.2208 76.2736 21.6124C76.2736 21.0344 76.7451 20.5628 77.3688 20.5628C77.9772 20.5628 78.4639 21.0344 78.4639 21.6124C78.4639 22.2208 77.9772 22.6771 77.3688 22.6771ZM74.8438 31.4385V30.3737H76.9733V24.8066L75.4066 24.9435V23.8788L77.1558 23.7267C77.8707 23.6658 78.1445 24.0613 78.1445 24.6393V30.3737H80.274V31.4385H74.8438ZM85.8172 24.7914C84.2201 24.7914 83.0945 26.0539 83.0945 28.0921V31.4385H81.8472V23.8788H83.0945V25.1869L82.8968 26.0539H83.2466C83.5508 24.6849 84.5091 23.7267 86.1519 23.7267C87.8555 23.7267 88.9658 24.7154 88.9658 26.8144V31.4385H87.7338V26.9817C87.7338 25.3998 87.1101 24.7914 85.8172 24.7914ZM98.7436 23.8788H99.4128V24.9435H98.774C98.3481 24.9435 98.0743 25.0348 97.7853 25.2477C98.1047 25.6432 98.3025 26.1908 98.3025 26.8449C98.3025 28.4724 97.0248 29.4915 94.7432 30.3281C96.5076 30.0391 98.2112 30.2825 98.2112 31.9556C98.2112 33.9634 96.1426 34.222 91.0774 34.222V33.1573C95.4124 33.1573 96.9639 33.0508 96.9639 31.8644C96.9639 30.1912 93.6328 30.9974 91.7619 31.5602L91.5033 30.6932C92.7202 30.3585 93.9218 30.0087 95.093 29.6436V29.2938C92.6593 29.9174 90.5298 29.1265 90.5298 26.784C90.5298 24.7001 92.2334 23.7267 94.4085 23.7267C95.3212 23.7267 96.2642 23.9548 96.9791 24.4568C97.7701 23.9548 98.0591 23.8788 98.7436 23.8788ZM94.4237 28.8527C96.0209 28.8527 97.0552 28.2899 97.0552 26.8144C97.0552 25.3542 96.0209 24.7914 94.4237 24.7914C92.8418 24.7914 91.7771 25.3542 91.7771 26.8144C91.7771 28.2899 92.8418 28.8527 94.4237 28.8527ZM111.904 29.4763C111.904 30.6171 111.189 31.5906 108.922 31.5906C106.473 31.5906 105.47 30.4498 105.47 28.6549H106.717C106.717 29.963 107.477 30.5715 108.938 30.5715C110.109 30.5715 110.656 30.2977 110.656 29.5219C110.656 28.5332 109.637 28.3659 108.405 27.9857C106.975 27.5293 105.819 27.0882 105.819 25.7193C105.819 24.5176 106.839 23.7267 108.573 23.7267C110.93 23.7267 111.645 25.0196 111.645 26.2668H110.398C110.398 25.2477 109.774 24.7458 108.573 24.7458C107.736 24.7458 107.067 24.9892 107.067 25.6736C107.067 26.4037 107.584 26.6319 108.801 27.0274C110.291 27.5141 111.904 27.8488 111.904 29.4763ZM113.182 27.651C113.182 25.4759 114.597 23.7267 117.046 23.7267C118.506 23.7267 120.377 24.548 120.575 26.7384H119.312C119.099 25.2934 118.141 24.7914 117.046 24.7914C115.251 24.7914 114.43 26.0995 114.43 27.651C114.43 29.2177 115.251 30.5258 117.046 30.5258C118.141 30.5258 119.099 30.0239 119.312 28.5789H120.575C120.377 30.7692 118.506 31.5906 117.046 31.5906C114.597 31.5906 113.182 29.8414 113.182 27.651ZM126.689 23.7267C128.393 23.7267 129.503 24.7154 129.503 26.8144V31.4385H128.271V26.9817C128.271 25.3998 127.647 24.7914 126.354 24.7914C124.757 24.7914 123.632 26.0539 123.632 28.0921V31.4385H122.384V20.2586H123.632V25.1869L123.434 26.0539H123.784C124.088 24.6849 125.046 23.7267 126.689 23.7267ZM131.215 27.651C131.215 25.4607 132.797 23.7267 135.216 23.7267C137.619 23.7267 139.186 25.4607 139.186 27.651C139.186 29.8566 137.619 31.5906 135.216 31.5906C132.797 31.5906 131.215 29.8566 131.215 27.651ZM132.463 27.651C132.463 29.2329 133.421 30.5258 135.216 30.5258C136.995 30.5258 137.954 29.2329 137.954 27.651C137.954 26.0843 136.995 24.7914 135.216 24.7914C133.421 24.7914 132.463 26.0843 132.463 27.651ZM140.618 27.651C140.618 25.4607 142.2 23.7267 144.618 23.7267C147.022 23.7267 148.588 25.4607 148.588 27.651C148.588 29.8566 147.022 31.5906 144.618 31.5906C142.2 31.5906 140.618 29.8566 140.618 27.651ZM141.865 27.651C141.865 29.2329 142.824 30.5258 144.618 30.5258C146.398 30.5258 147.356 29.2329 147.356 27.651C147.356 26.0843 146.398 24.7914 144.618 24.7914C142.824 24.7914 141.865 26.0843 141.865 27.651ZM149.349 31.4385V30.3737H151.478V21.1865L149.912 21.3234V20.2586L151.676 20.1065C152.376 20.0457 152.71 20.4412 152.71 21.1561V30.3737H154.825V31.4385H149.349Z"
              fill="white"
            />
            <line
              x1="41.2191"
              y1="5.43848"
              x2="41.2191"
              y2="34.7241"
              stroke="white"
              stroke-width="0.563185"
            />
            <path
              d="M9.55556 18.7654C9.14558 18.6132 8.73888 18.4617 8.3342 18.311C7.57197 18.027 6.81692 17.7458 6.06066 17.4678C5.87433 17.3993 5.74595 17.3 5.64886 17.1177C4.83662 15.5929 4.01663 14.0722 3.1903 12.555C3.07318 12.34 3.069 12.1596 3.15412 11.9258C3.48081 11.0285 3.74959 10.1127 4.01838 9.19681C4.13973 8.78333 4.26108 8.36985 4.38776 7.95805C4.58101 7.32987 4.7677 6.69948 4.94401 6.06638C4.99443 5.88534 5.08556 5.75256 5.22858 5.63759C5.39411 5.50454 5.56005 5.37195 5.726 5.23937C6.29488 4.78484 6.86382 4.33027 7.41627 3.85651C7.69443 3.61797 8.0152 3.5714 8.33757 3.5246C8.39158 3.51676 8.44564 3.50892 8.49955 3.50016C10.1662 3.22937 11.8339 2.96386 13.5039 2.7149C13.8187 2.66798 13.9158 2.52532 13.92 2.2288C13.9308 1.469 13.9516 1.45701 14.5957 1.85268C16.4284 2.9785 18.2592 4.10762 20.0839 5.24627C20.3206 5.39396 20.4525 5.38951 20.6268 5.15238C20.8508 4.84742 21.0867 4.55117 21.3226 4.25491C21.5261 3.99932 21.7296 3.74373 21.9256 3.4825C22.0803 3.27622 22.2699 3.18017 22.5095 3.13808C22.6331 3.11637 22.7567 3.09483 22.8803 3.0733C23.4039 2.98209 23.9276 2.89086 24.4485 2.78618C24.7832 2.71893 24.9723 2.79198 25.0555 3.15756C25.1046 3.37297 25.1759 3.58326 25.2473 3.79359C25.3157 3.99533 25.3842 4.1971 25.433 4.40346C25.5088 4.72405 25.6547 4.81053 25.9725 4.75395C27.0464 4.56277 28.1234 4.38799 29.2016 4.22168C29.2478 4.21455 29.2975 4.20043 29.3483 4.18602C29.5113 4.13978 29.6851 4.09046 29.7876 4.25873C29.8747 4.4017 29.7714 4.54072 29.6743 4.67136C29.6443 4.7117 29.6149 4.75125 29.5918 4.78986C29.3453 5.20364 29.0915 5.61492 28.8115 6.0063C28.6444 6.23982 28.6828 6.37893 28.8823 6.5669C29.2023 6.86831 29.5156 7.17729 29.8289 7.48621C30.4122 8.06132 30.9953 8.63625 31.6201 9.16183C32.1809 9.63357 32.3403 10.1758 32.3754 10.8382C32.397 11.2464 32.3488 11.6188 32.1631 11.9939C31.7671 12.794 31.4021 13.6097 31.0394 14.4257C30.9247 14.6836 30.7914 14.766 30.5245 14.6322C30.307 14.5232 30.081 14.4309 29.8552 14.3387C29.787 14.3109 29.7189 14.2831 29.6511 14.2548C29.484 14.1852 29.43 14.0811 29.4707 13.8946C29.6108 13.2531 29.7377 12.6087 29.8626 11.964C29.8912 11.8164 29.9698 11.7075 30.0654 11.6017C30.1779 11.4772 30.2888 11.3514 30.4197 11.2029C30.48 11.1345 30.5445 11.0612 30.6154 10.9811C30.376 10.9178 30.1518 10.8123 29.9317 10.7087C29.4153 10.4657 28.9218 10.2335 28.3091 10.5816C28.2585 10.6103 28.203 10.6302 28.1475 10.6501C28.0916 10.6701 28.0358 10.6901 27.985 10.7191C27.2071 11.1631 26.5322 10.9554 25.9068 10.4028C25.8807 10.3796 25.8466 10.3655 25.803 10.3474C25.7794 10.3376 25.7531 10.3267 25.7238 10.3126C25.5289 10.7339 25.4304 11.167 25.3441 11.5967C25.3151 11.741 25.4413 11.8292 25.5599 11.9121C25.5963 11.9375 25.6321 11.9625 25.6624 11.9885C26.7495 12.92 27.8392 13.8485 28.9289 14.777C29.3066 15.0988 29.6842 15.4206 30.0618 15.7425C30.1674 15.8326 30.2732 15.9226 30.379 16.0126C30.7299 16.3111 31.0808 16.6096 31.4278 16.9125C31.8701 17.2986 31.7687 17.9797 31.2417 18.1977C30.9459 18.32 30.6499 18.4419 30.3539 18.5639C29.607 18.8716 28.86 19.1793 28.1157 19.4934C27.8662 19.5987 27.6339 19.6265 27.3661 19.5567C26.9788 19.4559 26.5891 19.3642 26.1994 19.2724C25.8465 19.1894 25.4936 19.1064 25.1424 19.0165C24.9176 18.959 24.7167 18.9706 24.5424 19.1106C23.8549 19.6627 23.211 19.6514 22.497 19.1026C22.0933 18.7924 21.7164 18.5008 21.4278 18.0677C19.9925 15.9141 18.5434 13.7697 17.0943 11.6253C16.7795 11.1595 16.4647 10.6937 16.1501 10.2278C16.1344 10.2046 16.1195 10.1804 16.1045 10.156C16.0403 10.0516 15.9743 9.94422 15.8364 9.89889C15.6477 9.99486 15.5281 10.1605 15.4097 10.3244C15.3781 10.3681 15.3466 10.4117 15.3139 10.4539C15.1804 10.6259 15.0285 10.6769 14.8242 10.645C14.5241 10.598 14.2237 10.5529 13.9232 10.5078C13.2842 10.4118 12.6452 10.3159 12.0093 10.202C11.7264 10.1513 11.4918 10.1777 11.2912 10.3649C10.8675 10.76 10.3713 10.752 9.8414 10.6931C9.74881 10.6829 9.65597 10.6703 9.56294 10.6578C9.24378 10.6147 8.92234 10.5713 8.60109 10.6176C8.56563 10.796 8.6615 10.8756 8.74916 10.9485C8.76697 10.9633 8.78444 10.9778 8.80041 10.9928C9.10816 11.2822 9.41838 11.5689 9.72861 11.8557C9.99674 12.1035 10.2649 12.3514 10.5314 12.6009C10.6696 12.7303 10.8279 12.7818 11.0101 12.7903C11.3457 12.8058 11.6813 12.822 12.0169 12.8381C12.7253 12.8721 13.4337 12.9061 14.1423 12.935C14.361 12.9439 14.5444 13.0141 14.7394 13.11C15.8122 13.6371 16.6313 14.4322 17.2963 15.4137C17.4968 15.7095 17.7216 15.9886 17.9466 16.2679C18.0687 16.4194 18.1907 16.571 18.309 16.7252C18.5697 17.0654 18.5234 17.2198 18.1231 17.3206C17.1786 17.5584 16.2339 17.7954 15.2892 18.0324C13.8312 18.3981 12.3732 18.7638 10.916 19.1328C10.7568 19.1731 10.6202 19.164 10.473 19.1065C10.2361 19.014 9.99712 18.9267 9.74484 18.8346C9.68261 18.8119 9.61958 18.7888 9.55556 18.7654Z"
              fill="white"
            />
            <path
              d="M23.4111 21.8196C23.6012 21.8656 23.7882 21.9118 23.9732 21.9575C24.3591 22.0528 24.7363 22.146 25.1154 22.2306C25.3099 22.274 25.4595 22.3592 25.5884 22.5079C25.6014 22.5229 25.6144 22.5379 25.6274 22.5529C26.0836 23.0795 26.54 23.6062 27.0091 24.1213C27.1609 24.288 27.207 24.4412 27.137 24.6588C26.95 25.2395 26.7742 25.8241 26.6094 26.4114C26.5569 26.5981 26.4524 26.7094 26.2918 26.8055C24.0945 28.1199 21.8975 29.4349 19.7078 30.7621C19.3907 30.9543 19.1762 30.9446 18.9225 30.6522C18.2182 29.8404 17.4985 29.0418 16.7789 28.2434C16.6412 28.0906 16.5035 27.9379 16.366 27.785C16.2329 27.6372 16.1666 27.4826 16.1697 27.2789C16.1795 26.6329 16.1798 25.9865 16.1697 25.3404C16.1664 25.1275 16.2282 24.9572 16.3571 24.7913C17.2941 23.5853 18.2307 22.379 19.1549 21.1633C19.3419 20.9174 19.5533 20.8677 19.8302 20.9366C21.0138 21.2311 22.1983 21.522 23.4111 21.8196Z"
              fill="white"
            />
            <path
              d="M27.8991 27.9604C28.089 27.8088 28.2571 27.8272 28.4452 27.9168C29.2193 28.2856 29.9967 28.6473 30.7757 29.0057C30.9258 29.0748 31.0071 29.1666 31.052 29.3347C31.364 30.5018 31.6884 31.6656 32.0133 32.8292C32.09 33.1041 32.0356 33.3271 31.8326 33.5354C30.8076 34.587 29.7855 35.6416 28.7732 36.7055C28.6024 36.885 28.4347 36.8701 28.2331 36.8181C27.9008 36.7323 27.5677 36.6491 27.2347 36.566C26.5321 36.3906 25.8296 36.2152 25.1343 36.0149C24.5612 35.8498 24.0113 35.7985 23.45 36.0118C23.1239 36.1358 22.966 36.0225 22.8647 35.6894C22.5541 34.6678 22.2249 33.6513 21.8792 32.641C21.7663 32.3111 21.8561 32.1221 22.1302 31.9345C23.5792 30.9431 25.024 29.9455 26.4781 28.9413C26.9506 28.615 27.4241 28.288 27.8991 27.9604Z"
              fill="white"
            />
            <path
              d="M32.7428 20.6878C32.8112 21.621 32.876 22.5214 32.9393 23.422C32.9545 23.6388 32.9689 23.8556 32.9833 24.0724C33.0085 24.4522 33.0337 24.832 33.0634 25.2115C33.083 25.4615 33.0719 25.6983 32.9616 25.9321C32.7111 26.463 32.4679 26.9976 32.2342 27.5361C32.1051 27.8335 31.9218 27.8577 31.6812 27.6829C31.353 27.4445 31.0252 27.2054 30.6975 26.9663C30.2092 26.6102 29.721 26.254 29.2312 25.9001C29.0692 25.783 29.0292 25.6272 29.0207 25.4423C28.9909 24.7977 28.958 24.1532 28.9171 23.5092C28.9072 23.354 28.9632 23.2394 29.0554 23.1257C29.738 22.2837 30.4199 21.4409 31.1017 20.5982C31.4467 20.1717 31.7918 19.7452 32.137 19.3188C32.1389 19.3164 32.1409 19.3139 32.1429 19.3115C32.2385 19.1932 32.3415 19.0658 32.5155 19.1316C32.6535 19.1837 32.6562 19.3076 32.6588 19.4279C32.6593 19.4512 32.6598 19.4745 32.6613 19.497C32.6868 19.883 32.7127 20.2691 32.7428 20.6878Z"
              fill="white"
            />
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M8.3342 18.311C7.57197 18.027 6.81692 17.7458 6.06066 17.4678C5.87433 17.3993 5.74595 17.3 5.64886 17.1177C4.83662 15.5929 4.01663 14.0722 3.1903 12.555C3.07318 12.34 3.069 12.1596 3.15412 11.9258C3.48081 11.0285 3.74959 10.1127 4.01838 9.19681C4.13973 8.78333 4.26108 8.36985 4.38776 7.95805C4.58101 7.32987 4.7677 6.69948 4.94401 6.06638C4.99443 5.88534 5.08556 5.75256 5.22858 5.63759C5.39411 5.50454 5.56005 5.37195 5.726 5.23937C6.29488 4.78484 6.86382 4.33027 7.41627 3.85651C7.69443 3.61797 8.0152 3.5714 8.33757 3.5246C8.39158 3.51676 8.44564 3.50892 8.49955 3.50016C10.1662 3.22937 11.8339 2.96386 13.5039 2.7149C13.8187 2.66798 13.9158 2.52532 13.92 2.2288C13.9308 1.469 13.9516 1.45701 14.5957 1.85268C16.4284 2.9785 18.2592 4.10762 20.0839 5.24627C20.3206 5.39396 20.4525 5.38951 20.6268 5.15238C20.8508 4.84742 21.0867 4.55117 21.3226 4.25491C21.5261 3.99932 21.7296 3.74373 21.9256 3.4825C22.0803 3.27622 22.2699 3.18017 22.5095 3.13808C22.6331 3.11637 22.7567 3.09483 22.8803 3.0733C23.4039 2.98209 23.9276 2.89086 24.4485 2.78618C24.7832 2.71893 24.9723 2.79198 25.0555 3.15756C25.1046 3.37297 25.1759 3.58326 25.2473 3.79359C25.3157 3.99533 25.3842 4.1971 25.433 4.40346C25.5088 4.72405 25.6547 4.81053 25.9725 4.75395C27.0464 4.56277 28.1234 4.38799 29.2016 4.22168C29.2478 4.21455 29.2975 4.20043 29.3483 4.18602C29.5113 4.13978 29.6851 4.09046 29.7876 4.25873C29.8747 4.4017 29.7714 4.54072 29.6743 4.67136C29.6443 4.7117 29.6149 4.75125 29.5918 4.78986C29.3453 5.20364 29.0915 5.61492 28.8115 6.0063C28.6444 6.23982 28.6828 6.37893 28.8823 6.5669C29.2023 6.86831 29.5156 7.17729 29.8289 7.48621C30.4122 8.06132 30.9953 8.63625 31.6201 9.16183C32.1809 9.63357 32.3403 10.1758 32.3754 10.8382C32.397 11.2464 32.3488 11.6188 32.1631 11.9939C31.7671 12.794 31.4021 13.6097 31.0394 14.4257C30.9247 14.6836 30.7914 14.766 30.5245 14.6322C30.307 14.5232 30.081 14.4309 29.8552 14.3387C29.787 14.3109 29.7189 14.2831 29.6511 14.2548C29.484 14.1852 29.43 14.0811 29.4707 13.8946C29.6108 13.2531 29.7377 12.6087 29.8626 11.964C29.8912 11.8164 29.9698 11.7075 30.0654 11.6017C30.1779 11.4772 30.2888 11.3514 30.4197 11.2029C30.48 11.1345 30.5445 11.0612 30.6154 10.9811C30.376 10.9178 30.1518 10.8123 29.9317 10.7087C29.4153 10.4657 28.9218 10.2335 28.3091 10.5816C28.2585 10.6103 28.203 10.6302 28.1475 10.6501C28.0916 10.6701 28.0358 10.6901 27.985 10.7191C27.2071 11.1631 26.5322 10.9554 25.9068 10.4028C25.8807 10.3796 25.8466 10.3655 25.803 10.3474C25.7794 10.3376 25.7531 10.3267 25.7238 10.3126C25.5289 10.7339 25.4304 11.167 25.3441 11.5967C25.3151 11.741 25.4413 11.8292 25.5599 11.9121C25.5963 11.9375 25.6321 11.9625 25.6624 11.9885C26.7495 12.92 27.8392 13.8485 28.9289 14.777C29.3066 15.0988 29.6842 15.4206 30.0618 15.7425C30.1674 15.8326 30.2732 15.9226 30.379 16.0126C30.7299 16.3111 31.0808 16.6096 31.4278 16.9125C31.8701 17.2986 31.7687 17.9797 31.2417 18.1977C30.9459 18.32 30.6499 18.4419 30.3539 18.5639C29.607 18.8716 28.86 19.1793 28.1157 19.4934C27.8662 19.5987 27.6339 19.6265 27.3661 19.5567C26.9788 19.4559 26.5891 19.3642 26.1994 19.2724C25.8465 19.1894 25.4936 19.1064 25.1424 19.0165C24.9176 18.959 24.7167 18.9706 24.5424 19.1106C23.8549 19.6627 23.211 19.6514 22.497 19.1026C22.0933 18.7924 21.7164 18.5008 21.4278 18.0677C19.9925 15.9141 18.5434 13.7697 17.0943 11.6253C16.7795 11.1595 16.4647 10.6937 16.1501 10.2278C16.1344 10.2046 16.1195 10.1804 16.1045 10.156C16.0403 10.0516 15.9743 9.94422 15.8364 9.89889C15.6477 9.99486 15.5281 10.1605 15.4097 10.3244C15.3781 10.3681 15.3466 10.4117 15.3139 10.4539C15.1804 10.6259 15.0285 10.6769 14.8242 10.645C14.5241 10.598 14.2237 10.5529 13.9232 10.5078C13.2842 10.4118 12.6452 10.3159 12.0093 10.202C11.7264 10.1513 11.4918 10.1777 11.2912 10.3649C10.8675 10.76 10.3713 10.752 9.8414 10.6931C9.74881 10.6829 9.65597 10.6703 9.56294 10.6578C9.24378 10.6147 8.92234 10.5713 8.60109 10.6176C8.56563 10.796 8.6615 10.8756 8.74916 10.9485C8.76697 10.9633 8.78444 10.9778 8.80041 10.9928C9.10816 11.2822 9.41838 11.5689 9.72861 11.8557C9.99674 12.1035 10.2649 12.3514 10.5314 12.6009C10.6696 12.7303 10.8279 12.7818 11.0101 12.7903C11.3457 12.8058 11.6813 12.822 12.0169 12.8381C12.7253 12.8721 13.4337 12.9061 14.1423 12.935C14.361 12.9439 14.5444 13.0141 14.7394 13.11C15.8122 13.6371 16.6313 14.4322 17.2963 15.4137C17.4968 15.7095 17.7216 15.9886 17.9466 16.2679C18.0687 16.4194 18.1907 16.571 18.309 16.7252C18.5697 17.0654 18.5234 17.2198 18.1231 17.3206C17.1786 17.5584 16.2339 17.7954 15.2892 18.0324C13.8312 18.3981 12.3732 18.7638 10.916 19.1328C10.7568 19.1731 10.6202 19.164 10.473 19.1065C10.2361 19.014 9.99712 18.9267 9.74484 18.8346C9.68261 18.8119 9.61958 18.7888 9.55556 18.7654ZM23.9732 21.9575C24.3591 22.0528 24.7363 22.146 25.1154 22.2306C25.3099 22.274 25.4595 22.3592 25.5884 22.5079L25.6274 22.5529C26.0836 23.0795 26.54 23.6062 27.0091 24.1213C27.1609 24.288 27.207 24.4412 27.137 24.6588C26.95 25.2395 26.7742 25.8241 26.6094 26.4114C26.5569 26.5981 26.4524 26.7094 26.2918 26.8055C24.0945 28.1199 21.8975 29.4349 19.7078 30.7621C19.3907 30.9543 19.1762 30.9446 18.9225 30.6522C18.2182 29.8404 17.4985 29.0418 16.7789 28.2434C16.6412 28.0906 16.5035 27.9379 16.366 27.785C16.2329 27.6372 16.1666 27.4826 16.1697 27.2789C16.1795 26.6329 16.1798 25.9865 16.1697 25.3404C16.1664 25.1275 16.2282 24.9572 16.3571 24.7913C17.2941 23.5853 18.2307 22.379 19.1549 21.1633C19.3419 20.9174 19.5533 20.8677 19.8302 20.9366C21.0138 21.2311 22.1983 21.522 23.4111 21.8196ZM27.8991 27.9604C28.089 27.8088 28.2571 27.8272 28.4452 27.9168C29.2193 28.2856 29.9967 28.6473 30.7757 29.0057C30.9258 29.0748 31.0071 29.1666 31.052 29.3347C31.364 30.5018 31.6884 31.6656 32.0133 32.8292C32.09 33.1041 32.0356 33.3271 31.8326 33.5354C30.8076 34.587 29.7855 35.6416 28.7732 36.7055C28.6024 36.885 28.4347 36.8701 28.2331 36.8181C27.9008 36.7323 27.5677 36.6491 27.2347 36.566C26.5321 36.3906 25.8296 36.2152 25.1343 36.0149C24.5612 35.8498 24.0113 35.7985 23.45 36.0118C23.1239 36.1358 22.966 36.0225 22.8647 35.6894C22.5541 34.6678 22.2249 33.6513 21.8792 32.641C21.7663 32.3111 21.8561 32.1221 22.1302 31.9345C23.5792 30.9431 25.024 29.9455 26.4781 28.9413C26.9506 28.615 27.4241 28.288 27.8991 27.9604ZM32.7428 20.6878C32.8112 21.621 32.876 22.5214 32.9393 23.422C32.9545 23.6388 32.9689 23.8556 32.9833 24.0724C33.0085 24.4522 33.0337 24.832 33.0634 25.2115C33.083 25.4615 33.0719 25.6983 32.9616 25.9321C32.7111 26.463 32.4679 26.9976 32.2342 27.5361C32.1051 27.8335 31.9218 27.8577 31.6812 27.6829C31.353 27.4445 31.0252 27.2054 30.6975 26.9663C30.2092 26.6102 29.721 26.254 29.2312 25.9001C29.0692 25.783 29.0292 25.6272 29.0207 25.4423C28.9909 24.7977 28.958 24.1532 28.9171 23.5092C28.9072 23.354 28.9632 23.2394 29.0554 23.1257C29.738 22.2837 30.4199 21.4409 31.1017 20.5982C31.4467 20.1717 31.7918 19.7452 32.137 19.3188L32.1429 19.3115C32.2385 19.1932 32.3415 19.0658 32.5155 19.1316C32.6535 19.1837 32.6562 19.3076 32.6588 19.4279C32.6593 19.4512 32.6598 19.4745 32.6613 19.497C32.6868 19.883 32.7127 20.2691 32.7428 20.6878Z"
              fill="white"
            />
          </svg>
        </a>
      </div>
      <div class="">
        <a href="/kodex/registration">
          <button
            class="border font-[urbanians] tracking-[.4px] mt-2.5 flex gap-9 border-white/[0.1] transition-all ease duration-500 bg-grad-hover text-[.72rem] bg-grad inner-shadow px-[2rem] font-thin py-[.58rem] rounded-full text-white/50 transition-all relative z-[9999999]"
          >
            Enroll Now
          </button>
        </a>
      </div>
    </nav>

    <div class="absolute right-0 top-[25%] z-[-1]">
      <svg
        width="158"
        height="209"
        viewBox="0 0 158 209"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g filter="url(#filter0_ddddi_907_1397)">
          <rect
            x="14"
            y="69.1801"
            width="126.132"
            height="126.132"
            rx="18.9198"
            transform="rotate(-17.6199 14 69.1801)"
            fill="url(#paint0_linear_907_1397)"
          />
          <rect
            x="14.8345"
            y="69.6123"
            width="124.803"
            height="124.803"
            rx="18.2552"
            transform="rotate(-17.6199 14.8345 69.6123)"
            stroke="#282828"
            stroke-width="1.32905"
          />
        </g>
        <g filter="url(#filter1_i_907_1397)">
          <path
            d="M115.41 118.42L122.913 103.049L107.542 95.5461M73.2304 107.348L65.7272 122.719L81.0982 130.222M93.6003 87.5512L95.0396 138.217"
            stroke="url(#paint1_linear_907_1397)"
            stroke-width="6.04738"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </g>
        <defs>
          <filter
            id="filter0_ddddi_907_1397"
            x="0.391126"
            y="0.273311"
            width="200.027"
            height="207.912"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feMorphology
              radius="17.2776"
              operator="erode"
              in="SourceAlpha"
              result="effect1_dropShadow_907_1397"
            />
            <feOffset dx="-11.7122" dy="21.6226" />
            <feGaussianBlur stdDeviation="9.64007" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_907_1397"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="3.60377" />
            <feGaussianBlur stdDeviation="7.04396" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"
            />
            <feBlend
              mode="normal"
              in2="effect1_dropShadow_907_1397"
              result="effect2_dropShadow_907_1397"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feMorphology
              radius="26.581"
              operator="erode"
              in="SourceAlpha"
              result="effect3_dropShadow_907_1397"
            />
            <feOffset dx="7.20753" dy="-9.91035" />
            <feGaussianBlur stdDeviation="26.1158" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"
            />
            <feBlend
              mode="normal"
              in2="effect2_dropShadow_907_1397"
              result="effect3_dropShadow_907_1397"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feMorphology
              radius="2.70282"
              operator="erode"
              in="SourceAlpha"
              result="effect4_dropShadow_907_1397"
            />
            <feOffset dx="0.900941" dy="-7.20753" />
            <feGaussianBlur stdDeviation="2.43254" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.46 0"
            />
            <feBlend
              mode="normal"
              in2="effect3_dropShadow_907_1397"
              result="effect4_dropShadow_907_1397"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect4_dropShadow_907_1397"
              result="shape"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="5.31619" />
            <feGaussianBlur stdDeviation="2.45874" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.634615 0 0 0 0 0.634615 0 0 0 0 0.634615 0 0 0 0.25 0"
            />
            <feBlend
              mode="normal"
              in2="shape"
              result="effect5_innerShadow_907_1397"
            />
          </filter>
          <filter
            id="filter1_i_907_1397"
            x="61.6449"
            y="84.5275"
            width="64.2921"
            height="59.2384"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dx="-1.05793" dy="2.6581" />
            <feGaussianBlur stdDeviation="1.2626" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.322115 0 0 0 0 0.322115 0 0 0 0 0.322115 0 0 0 1 0"
            />
            <feBlend
              mode="normal"
              in2="shape"
              result="effect1_innerShadow_907_1397"
            />
          </filter>
          <linearGradient
            id="paint0_linear_907_1397"
            x1="77.0659"
            y1="69.1801"
            x2="77.0659"
            y2="195.312"
            gradientUnits="userSpaceOnUse"
          >
            <stop />
            <stop offset="1" stop-color="#272727" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_907_1397"
            x1="86.4521"
            y1="90.0099"
            x2="102.188"
            y2="135.758"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="white" />
            <stop offset="1" stop-color="#A9A9A9" />
          </linearGradient>
        </defs>
      </svg>
    </div>
    <div class="absolute left-0 top-[60%] z-[-1]">
      <svg
        width="100"
        height="162"
        viewBox="0 0 100 162"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g filter="url(#filter0_f_907_1401)">
          <g filter="url(#filter1_ddddi_907_1401)">
            <rect
              x="-44"
              y="53.9972"
              width="97.1503"
              height="97.1503"
              rx="14.5725"
              transform="rotate(-17.6199 -44 53.9972)"
              fill="url(#paint0_linear_907_1401)"
            />
            <rect
              x="-43.3572"
              y="54.3301"
              width="96.1266"
              height="96.1266"
              rx="14.0607"
              transform="rotate(-17.6199 -43.3572 54.3301)"
              stroke="#282828"
              stroke-width="1.02367"
            />
          </g>
          <g filter="url(#filter2_i_907_1401)">
            <path
              d="M34.108 91.923L39.8872 80.0838L28.048 74.3047M1.62045 83.3947L-4.15871 95.2339L7.68049 101.013M17.3099 68.1468L18.4186 107.171"
              stroke="url(#paint1_linear_907_1401)"
              stroke-width="4.65786"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </g>
        </g>
        <defs>
          <filter
            id="filter0_f_907_1401"
            x="-50.8202"
            y="17.7695"
            width="135.64"
            height="135.641"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feGaussianBlur
              stdDeviation="5.27191"
              result="effect1_foregroundBlur_907_1401"
            />
          </filter>
          <filter
            id="filter1_ddddi_907_1401"
            x="-54.4818"
            y="0.923206"
            width="154.066"
            height="160.14"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feMorphology
              radius="13.3077"
              operator="erode"
              in="SourceAlpha"
              result="effect1_dropShadow_907_1401"
            />
            <feOffset dx="-9.0211" dy="16.6543" />
            <feGaussianBlur stdDeviation="7.42506" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_907_1401"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="2.77572" />
            <feGaussianBlur stdDeviation="5.42546" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"
            />
            <feBlend
              mode="normal"
              in2="effect1_dropShadow_907_1401"
              result="effect2_dropShadow_907_1401"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feMorphology
              radius="20.4734"
              operator="erode"
              in="SourceAlpha"
              result="effect3_dropShadow_907_1401"
            />
            <feOffset dx="5.55145" dy="-7.63324" />
            <feGaussianBlur stdDeviation="20.1151" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"
            />
            <feBlend
              mode="normal"
              in2="effect2_dropShadow_907_1401"
              result="effect3_dropShadow_907_1401"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feMorphology
              radius="2.08179"
              operator="erode"
              in="SourceAlpha"
              result="effect4_dropShadow_907_1401"
            />
            <feOffset dx="0.693931" dy="-5.55145" />
            <feGaussianBlur stdDeviation="1.87361" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.46 0"
            />
            <feBlend
              mode="normal"
              in2="effect3_dropShadow_907_1401"
              result="effect4_dropShadow_907_1401"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect4_dropShadow_907_1401"
              result="shape"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="4.09468" />
            <feGaussianBlur stdDeviation="1.89379" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.634615 0 0 0 0 0.634615 0 0 0 0 0.634615 0 0 0 0.25 0"
            />
            <feBlend
              mode="normal"
              in2="shape"
              result="effect5_innerShadow_907_1401"
            />
          </filter>
          <filter
            id="filter2_i_907_1401"
            x="-7.30313"
            y="65.8178"
            width="49.5197"
            height="45.6271"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dx="-0.814849" dy="2.04734" />
            <feGaussianBlur stdDeviation="0.972487" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.322115 0 0 0 0 0.322115 0 0 0 0 0.322115 0 0 0 1 0"
            />
            <feBlend
              mode="normal"
              in2="shape"
              result="effect1_innerShadow_907_1401"
            />
          </filter>
          <linearGradient
            id="paint0_linear_907_1401"
            x1="4.57515"
            y1="53.9972"
            x2="4.57515"
            y2="151.147"
            gradientUnits="userSpaceOnUse"
          >
            <stop />
            <stop offset="1" stop-color="#272727" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_907_1401"
            x1="11.8042"
            y1="70.0405"
            x2="23.9243"
            y2="105.277"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="white" />
            <stop offset="1" stop-color="#A9A9A9" />
          </linearGradient>
        </defs>
      </svg>
    </div>

    <div
      class="h-full formWrappper max-w-[90%] mx-auto mt-7 sm:mt-0 flex items-center justify-center"
    >
      <form
        id="codex-form"
        action="/kodex/submission"
        class="w-full sm:w-[80%] md:w-[60%] lg:w-[40rem] xl:w-[33%] max-w-[500px] inner-shadow bg-grad-box mx-auto mt-12 p-8 px-12 bg-opacity-20 border border-white/[0.1]"
      >
        <h2 class="text-3xl text-center text-white mt-4 mb-8">Enroll Now</h2>

        <div class="mb-4">
          <label for="fullName" class="block text-white/60 text-sm mb-1.5"
            >Full Name</label
          >
          <input
            type="text"
            id="fullName"
            placeholder="Enter Your Full Name"
            class="w-full bg-grad-input text-[.7rem] px-4 py-3 border border-white/[0.15] rounded-full text-white focus:outline-none"
          />
        </div>
        <div class="mb-4">
          <label
            for="email"
            class="block text-white/60 font-thin text-sm mb-1.5"
            >Email Address</label
          >
          <input
            type="email"
            id="email"
            placeholder="Enter Your Email Address"
            class="w-full bg-grad-input text-[.7rem] px-4 py-3 border border-white/[0.15] rounded-full text-white focus:outline-none"
          />
        </div>
        <div class="mb-4">
          <label
            for="phone"
            class="block text-white/60 font-thin text-sm mb-1.5"
            >Phone Number</label
          >
          <input
            type="tel"
            id="phone"
            placeholder="Enter Your Phone Number"
            class="w-full bg-grad-input font-thin text-[.7rem] px-4 py-3 border border-white/[0.15] rounded-full text-white focus:outline-none"
          />
        </div>
        <div class="mb-4">
          <label for="dob" class="block text-white/60 font-thin text-sm mb-1.5"
            >Date Of Birth</label
          >
          <input
            type="date"
            id="dob"
            placeholder="Enter Your DOB"
            class="w-full bg-grad-input font-thin text-xs px-4 py-3 border border-white/[0.15] rounded-full text-white focus:outline-none [color-scheme:dark]"
            style="color: white"
          />
        </div>
        <div class="mb-4">
          <label
            for="experience"
            class="block text-white/60 font-thin text-sm mb-1.5"
            >Experience</label
          >
          <select
            id="experience"
            class="w-full bg-grad-input font-thin text-[.7rem] px-4 py-3 border border-white/[0.15] rounded-full text-white focus:outline-none"
          >
            <option value="" disabled selected>Select Your Experience</option>
            <option value="Fresher">Fresher</option>
            <option value="1-2 Years">1-2 Years</option>
            <option value="3+ Years">3+ Years</option>
          </select>
        </div>
        <div class="mb-6">
          <label
            for="degree"
            class="block text-white/60 font-thin text-sm mb-1.5"
            >Degree</label
          >
          <input
            type="text"
            id="degree"
            placeholder="Enter Your Degree"
            class="w-full bg-grad-input font-thin text-[.7rem] px-4 py-3 border border-white/[0.15] rounded-full text-white focus:outline-none"
          />
        </div>
        <div class="text-center mt-10">
          <button
            id="submit-btn"
            type="submit"
            class="text-white bg-[#AFAFAF]/60 mx-auto px-20 text-lg rounded-full py-1.5 font-medium border border-white/30 hover:bg-white/30 transition-all"
          >
            Submit
          </button>
          <div
            id="loading-indicator"
            style="display: none"
            class="mt-4 text-center"
          >
            <span
              class="loader inline-block w-6 h-6 border-4 border-gray-300 border-t-green-600 rounded-full animate-spin"
            ></span>
            <span class="ml-2 text-green-600">Submitting...</span>
          </div>
        </div>
      </form>
    </div>
  </body>
  <script
    src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.5.0/axios.min.js"
    integrity="sha512-aoTNnqZcT8B4AmeCFmiSnDlc4Nj/KPaZyB5G7JnOnUEkdNpCZs1LCankiYi01sLTyWy+m2P+W4XM+BuQ3Q4/Dg=="
    crossorigin="anonymous"
    referrerpolicy="no-referrer"
  ></script>
  <script>
    function showPopup(message, type = "error") {
      // Remove existing popup if any
      const oldPopup = document.getElementById("popup-message");
      if (oldPopup) oldPopup.remove();

      const popup = document.createElement("div");
      popup.id = "popup-message";
      popup.className =
        "fixed top-8 left-1/2 transform -translate-x-1/2 z-50 px-6 py-3 rounded-lg shadow-lg text-center transition-all duration-300 " +
        (type === "error"
          ? "bg-red-600/10 border-red-600 border  text-red-600"
          : "bg-green-600/10 border-green-600 border text-green-600");
      popup.textContent = message;
      document.body.appendChild(popup);

      setTimeout(() => {
        popup.remove();
      }, 3500);
    }

    document
      .querySelector("form")
      .addEventListener("submit", async function (e) {
        e.preventDefault();
        const submitBtn = document.getElementById("submit-btn");
        const loading = document.getElementById("loading-indicator");
        submitBtn.style.display = "none";
        loading.style.display = "block";

        const dobValue = document.getElementById("dob").value;
        if (!dobValue || new Date(dobValue) > new Date()) {
          showPopup("Invalid or missing date of birth");
          submitBtn.style.display = "block";
          loading.style.display = "none";
          return;
        }
        const data = {
          fullName: document.getElementById("fullName").value,
          email: document.getElementById("email").value,
          phone: document.getElementById("phone").value,
          dob: dobValue,
          experience: document.getElementById("experience").value,
          degree: document.getElementById("degree").value,
        };
        // Frontend validation
        if (!data.fullName || data.fullName.trim().length < 2) {
          showPopup("Invalid or missing name");
          submitBtn.style.display = "block";
          loading.style.display = "none";
          return;
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!data.email || !emailRegex.test(data.email)) {
          showPopup("Invalid or missing email");
          submitBtn.style.display = "block";
          loading.style.display = "none";
          return;
        }
        const phoneRegex = /^\d{10}$/;
        if (!data.phone || !phoneRegex.test(data.phone)) {
          showPopup("Invalid or missing contact number");
          submitBtn.style.display = "block";
          loading.style.display = "none";
          return;
        }
        if (!data.experience || data.experience.trim().length < 2) {
          showPopup("Invalid or missing experience");
          submitBtn.style.display = "block";
          loading.style.display = "none";
          return;
        }
        if (!data.degree || data.degree.trim().length < 2) {
          showPopup("Invalid or missing degree");
          submitBtn.style.display = "block";
          loading.style.display = "none";
          return;
        }
        try {
          const response = await axios.post("/kodex/submission", data);
          if (response.data && response.data.message) {
            showPopup(response.data.message, "success");
          } else {
            showPopup("Registration successful!", "success");
          }
          console.log(response.data);
          localStorage.setItem(
            "codexRegistered",
            JSON.stringify(response.data.id)
          );
          window.location.href = "/kodex/ranking?id=" + response.data.id;
        } catch (err) {
          let msg =
            (err.response && err.response.data && err.response.data.message) ||
            err.response?.data ||
            err.message ||
            "Something went wrong!";
          showPopup(msg, "error");
        } finally {
          submitBtn.disabled = false;
          loading.style.display = "none";
        }
      });
  </script>
</html>
```

<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="https://cdn.tailwindcss.com"></script>
        <title>Kodex | Ranking</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <style>
  </head>
  <style>
    .inner-shadow {
      box-shadow: inset 0.05px 0 10px rgba(255, 255, 255, 0.03);
    }
    .bg-grad {
      background: radial-gradient(#141414 70%, #1b1b1b);
    }
    .bg-grad-box {
      background-color: #2120205f;
      border-radius: 40px;
    }
    .bg-grad-hover:hover {
      background: radial-gradient(#1b1b1b 0%, #1b1b1b 100%);
    }
    .bg-grad-input {
      box-shadow: inset 4px 0 10px rgba(255, 255, 255, 0.1);
      background-color: #131313e3;
      font-weight: 100;
    }

    .custom-scrollbar::-webkit-scrollbar {
      width: 2px;
      background: transparent;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      width: 3px;
      background: linear-gradient(135deg, #525252 40%, #afafaf 100%);
      border-radius: 10px;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: transparent;
    }
    .custom-scrollbar {
      scrollbar-width: 3px;
      scrollbar-color: #afafaf #212020;
      max-height: 70vh;
    }

    @font-face {
      font-family: "urbanians";
      src: url("../Assets/fonts/Urbanist-Regular.ttf");
    }

    .fontThin {
      font-weight: 100;
    }

    .button {
      padding: 0.03rem;
      border-radius: 20px;
      height: fit-content;
      width: fit-content;
      background: linear-gradient(-90deg, #afafaf 0%, #303030 100%);
    }
    .HeroVideo {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      z-index: -1;
      filter: saturate(0) blur(4px);
      opacity: 40%;
    }
    .HeroVideoCover {
      position: fixed;
      z-index: -1;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #000000;
      background: radial-gradient(rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 60%);
    }
  </style>
  <body class="bg-black relative pt-1 font-[urbanians]">
    <!-- <div class="fixed top-0 left-0 h-full w-full">
      <video autoplay  src="./cfda4326e42bda5e9a3120f31640c4f9-ezgif.com-video-speed.m4v" class="h-full w-full object-cover grayscale-1 z-[-999999]"></video>
    </div> -->
    <video
      id="heroVdo"
      class="HeroVideo"
      src="/Assets/images/cfda4326e42bda5e9a3120f31640c4f9-ezgif.com-video-speed.m4v"
      autoplay
      loop
      muted
      playsinline
    ></video>
    <div class="HeroVideoCover">
      <div class="t"></div>
      <div class="g"></div>
      <div class="b"></div>
    </div>
    <nav
      class="w-full absolute top-[0%] justify-between mb-10 flex items-center lg:pl-[3.76rem] lg:pr-[3.7rem] md:pl-[2.5rem] md:pr-[2.7rem] px-[1.5rem] pt-[2.43rem]"
    >
      <div class="logo">
        <a href="/">
          <svg
            width="157"
            height="39"
            viewBox="0 0 157 39"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M53.096 14.4763C53.096 15.6171 52.3811 16.5906 50.1147 16.5906C47.6658 16.5906 46.6619 15.4498 46.6619 13.6549H47.9091C47.9091 14.963 48.6697 15.5715 50.1299 15.5715C51.3011 15.5715 51.8487 15.2977 51.8487 14.5219C51.8487 13.5332 50.8296 13.3659 49.5975 12.9857C48.1677 12.5293 47.0117 12.0882 47.0117 10.7193C47.0117 9.51762 48.0308 8.72666 49.7648 8.72666C52.1225 8.72666 52.8374 10.0196 52.8374 11.2668H51.5901C51.5901 10.2477 50.9665 9.74578 49.7648 9.74578C48.9283 9.74578 48.259 9.98915 48.259 10.6736C48.259 11.4037 48.7762 11.6319 49.993 12.0274C51.4837 12.5141 53.096 12.8488 53.096 14.4763ZM58.9687 8.72666C60.6723 8.72666 61.7827 9.71536 61.7827 11.8144V16.4385H60.5506V11.9817C60.5506 10.3998 59.927 9.79141 58.6341 9.79141C57.037 9.79141 55.9114 11.0539 55.9114 13.0921V16.4385H54.6641V5.25863H55.9114V10.1869L55.7136 11.0539H56.0635C56.3677 9.68494 57.326 8.72666 58.9687 8.72666ZM63.4952 12.5598C63.4952 10.126 65.0163 8.72666 67.161 8.72666C69.2905 8.72666 70.6899 10.0804 70.6899 12.4076V12.8944H64.7121C64.7881 14.3242 65.4726 15.5563 67.3435 15.5563C68.5908 15.5563 69.4274 14.9478 69.7012 13.7918H70.9484C70.6594 14.963 69.7468 16.5906 67.3435 16.5906C64.636 16.5906 63.4952 14.7349 63.4952 12.5598ZM64.7121 11.9513H69.473C69.473 10.7345 68.6668 9.7762 67.161 9.7762C65.6703 9.7762 64.7121 10.7345 64.7121 11.9513ZM72.6674 16.4385V9.7762C72.6674 9.0613 73.002 8.71145 73.7169 8.74187L77.9151 8.87877V9.94352L73.9146 9.80662V16.4385H72.6674ZM85.4049 8.87877H86.7586L83.4731 16.3624V18.4767C83.4731 19.0547 83.1385 19.4046 82.4236 19.3741L79.5031 19.2372V18.1725L82.2259 18.3094V16.3624L78.9251 8.87877H80.2941L82.667 14.7653V15.3433H83.032V14.7653L85.4049 8.87877ZM90.2919 7.67713C89.6683 7.67713 89.1967 7.22081 89.1967 6.61238C89.1967 6.03438 89.6683 5.56284 90.2919 5.56284C90.9003 5.56284 91.3871 6.03438 91.3871 6.61238C91.3871 7.22081 90.9003 7.67713 90.2919 7.67713ZM87.7669 16.4385V15.3737H89.8964V9.80662L88.3297 9.94352V8.87877L90.079 8.72666C90.7939 8.66582 91.0677 9.0613 91.0677 9.6393V15.3737H93.1971V16.4385H87.7669ZM94.481 14.3394C94.481 13.1378 95.1959 12.3164 96.9604 12.1339L99.9721 11.8449C99.896 10.4607 99.1507 9.7762 97.8426 9.7762C96.7474 9.7762 95.7283 10.2629 95.7283 11.6927H94.5114C94.5114 10.126 95.6979 8.72666 97.8426 8.72666C99.9569 8.72666 101.219 10.126 101.219 12.1491V15.3737H102.33V16.4385H100.991C100.413 16.4385 100.094 16.1191 100.094 15.5563V15.1456L100.292 14.3546H99.9264C99.6374 15.5106 98.8313 16.5906 97.006 16.5906C94.8157 16.5906 94.481 15.1304 94.481 14.3394ZM95.7283 14.1873C95.7283 15.0543 96.2759 15.5258 97.1581 15.5258C98.8769 15.5258 99.9721 14.3394 99.9721 12.7727L97.1733 13.0617C96.215 13.153 95.7283 13.442 95.7283 14.1873ZM107.727 9.79141C106.13 9.79141 105.004 11.0539 105.004 13.0921V16.4385H103.757V8.87877H105.004V10.1869L104.807 11.0539H105.157C105.461 9.68494 106.419 8.72666 108.062 8.72666C109.765 8.72666 110.876 9.71536 110.876 11.8144V16.4385H109.644V11.9817C109.644 10.3998 109.02 9.79141 107.727 9.79141ZM118.87 14.4763C118.87 15.6171 118.155 16.5906 115.889 16.5906C113.44 16.5906 112.436 15.4498 112.436 13.6549H113.683C113.683 14.963 114.444 15.5715 115.904 15.5715C117.075 15.5715 117.623 15.2977 117.623 14.5219C117.623 13.5332 116.604 13.3659 115.372 12.9857C113.942 12.5293 112.786 12.0882 112.786 10.7193C112.786 9.51762 113.805 8.72666 115.539 8.72666C117.897 8.72666 118.612 10.0196 118.612 11.2668H117.364C117.364 10.2477 116.741 9.74578 115.539 9.74578C114.703 9.74578 114.033 9.98915 114.033 10.6736C114.033 11.4037 114.55 11.6319 115.767 12.0274C117.258 12.5141 118.87 12.8488 118.87 14.4763ZM46.814 27.651C46.814 25.4759 48.2286 23.7267 50.6775 23.7267C52.1377 23.7267 54.0086 24.548 54.2064 26.7384H52.9439C52.7309 25.2934 51.7727 24.7914 50.6775 24.7914C48.8826 24.7914 48.0613 26.0995 48.0613 27.651C48.0613 29.2177 48.8826 30.5258 50.6775 30.5258C51.7727 30.5258 52.7309 30.0239 52.9439 28.5789H54.2064C54.0086 30.7692 52.1377 31.5906 50.6775 31.5906C48.2286 31.5906 46.814 29.8414 46.814 27.651ZM55.6225 27.651C55.6225 25.4607 57.2044 23.7267 59.6229 23.7267C62.0262 23.7267 63.5929 25.4607 63.5929 27.651C63.5929 29.8566 62.0262 31.5906 59.6229 31.5906C57.2044 31.5906 55.6225 29.8566 55.6225 27.651ZM56.8698 27.651C56.8698 29.2329 57.8281 30.5258 59.6229 30.5258C61.4026 30.5258 62.3608 29.2329 62.3608 27.651C62.3608 26.0843 61.4026 24.7914 59.6229 24.7914C57.8281 24.7914 56.8698 26.0843 56.8698 27.651ZM73.0869 31.4385H71.8396V30.3281L72.0525 29.4763H71.6875C71.368 30.6475 70.3489 31.5906 68.6301 31.5906C66.4398 31.5906 65.0252 29.9022 65.0252 27.651C65.0252 25.415 66.4398 23.7267 68.6301 23.7267C70.3489 23.7267 71.368 24.6697 71.6875 25.8562H72.0525L71.8396 24.9892V20.2586H73.0869V31.4385ZM71.8396 27.651C71.8396 25.6432 70.5619 24.7914 68.9952 24.7914C67.4285 24.7914 66.2725 25.6432 66.2725 27.651C66.2725 29.674 67.4285 30.5258 68.9952 30.5258C70.5619 30.5258 71.8396 29.674 71.8396 27.651ZM77.3688 22.6771C76.7451 22.6771 76.2736 22.2208 76.2736 21.6124C76.2736 21.0344 76.7451 20.5628 77.3688 20.5628C77.9772 20.5628 78.4639 21.0344 78.4639 21.6124C78.4639 22.2208 77.9772 22.6771 77.3688 22.6771ZM74.8438 31.4385V30.3737H76.9733V24.8066L75.4066 24.9435V23.8788L77.1558 23.7267C77.8707 23.6658 78.1445 24.0613 78.1445 24.6393V30.3737H80.274V31.4385H74.8438ZM85.8172 24.7914C84.2201 24.7914 83.0945 26.0539 83.0945 28.0921V31.4385H81.8472V23.8788H83.0945V25.1869L82.8968 26.0539H83.2466C83.5508 24.6849 84.5091 23.7267 86.1519 23.7267C87.8555 23.7267 88.9658 24.7154 88.9658 26.8144V31.4385H87.7338V26.9817C87.7338 25.3998 87.1101 24.7914 85.8172 24.7914ZM98.7436 23.8788H99.4128V24.9435H98.774C98.3481 24.9435 98.0743 25.0348 97.7853 25.2477C98.1047 25.6432 98.3025 26.1908 98.3025 26.8449C98.3025 28.4724 97.0248 29.4915 94.7432 30.3281C96.5076 30.0391 98.2112 30.2825 98.2112 31.9556C98.2112 33.9634 96.1426 34.222 91.0774 34.222V33.1573C95.4124 33.1573 96.9639 33.0508 96.9639 31.8644C96.9639 30.1912 93.6328 30.9974 91.7619 31.5602L91.5033 30.6932C92.7202 30.3585 93.9218 30.0087 95.093 29.6436V29.2938C92.6593 29.9174 90.5298 29.1265 90.5298 26.784C90.5298 24.7001 92.2334 23.7267 94.4085 23.7267C95.3212 23.7267 96.2642 23.9548 96.9791 24.4568C97.7701 23.9548 98.0591 23.8788 98.7436 23.8788ZM94.4237 28.8527C96.0209 28.8527 97.0552 28.2899 97.0552 26.8144C97.0552 25.3542 96.0209 24.7914 94.4237 24.7914C92.8418 24.7914 91.7771 25.3542 91.7771 26.8144C91.7771 28.2899 92.8418 28.8527 94.4237 28.8527ZM111.904 29.4763C111.904 30.6171 111.189 31.5906 108.922 31.5906C106.473 31.5906 105.47 30.4498 105.47 28.6549H106.717C106.717 29.963 107.477 30.5715 108.938 30.5715C110.109 30.5715 110.656 30.2977 110.656 29.5219C110.656 28.5332 109.637 28.3659 108.405 27.9857C106.975 27.5293 105.819 27.0882 105.819 25.7193C105.819 24.5176 106.839 23.7267 108.573 23.7267C110.93 23.7267 111.645 25.0196 111.645 26.2668H110.398C110.398 25.2477 109.774 24.7458 108.573 24.7458C107.736 24.7458 107.067 24.9892 107.067 25.6736C107.067 26.4037 107.584 26.6319 108.801 27.0274C110.291 27.5141 111.904 27.8488 111.904 29.4763ZM113.182 27.651C113.182 25.4759 114.597 23.7267 117.046 23.7267C118.506 23.7267 120.377 24.548 120.575 26.7384H119.312C119.099 25.2934 118.141 24.7914 117.046 24.7914C115.251 24.7914 114.43 26.0995 114.43 27.651C114.43 29.2177 115.251 30.5258 117.046 30.5258C118.141 30.5258 119.099 30.0239 119.312 28.5789H120.575C120.377 30.7692 118.506 31.5906 117.046 31.5906C114.597 31.5906 113.182 29.8414 113.182 27.651ZM126.689 23.7267C128.393 23.7267 129.503 24.7154 129.503 26.8144V31.4385H128.271V26.9817C128.271 25.3998 127.647 24.7914 126.354 24.7914C124.757 24.7914 123.632 26.0539 123.632 28.0921V31.4385H122.384V20.2586H123.632V25.1869L123.434 26.0539H123.784C124.088 24.6849 125.046 23.7267 126.689 23.7267ZM131.215 27.651C131.215 25.4607 132.797 23.7267 135.216 23.7267C137.619 23.7267 139.186 25.4607 139.186 27.651C139.186 29.8566 137.619 31.5906 135.216 31.5906C132.797 31.5906 131.215 29.8566 131.215 27.651ZM132.463 27.651C132.463 29.2329 133.421 30.5258 135.216 30.5258C136.995 30.5258 137.954 29.2329 137.954 27.651C137.954 26.0843 136.995 24.7914 135.216 24.7914C133.421 24.7914 132.463 26.0843 132.463 27.651ZM140.618 27.651C140.618 25.4607 142.2 23.7267 144.618 23.7267C147.022 23.7267 148.588 25.4607 148.588 27.651C148.588 29.8566 147.022 31.5906 144.618 31.5906C142.2 31.5906 140.618 29.8566 140.618 27.651ZM141.865 27.651C141.865 29.2329 142.824 30.5258 144.618 30.5258C146.398 30.5258 147.356 29.2329 147.356 27.651C147.356 26.0843 146.398 24.7914 144.618 24.7914C142.824 24.7914 141.865 26.0843 141.865 27.651ZM149.349 31.4385V30.3737H151.478V21.1865L149.912 21.3234V20.2586L151.676 20.1065C152.376 20.0457 152.71 20.4412 152.71 21.1561V30.3737H154.825V31.4385H149.349Z"
              fill="white"
            />
            <line
              x1="41.2191"
              y1="5.43848"
              x2="41.2191"
              y2="34.7241"
              stroke="white"
              stroke-width="0.563185"
            />
            <path
              d="M9.55556 18.7654C9.14558 18.6132 8.73888 18.4617 8.3342 18.311C7.57197 18.027 6.81692 17.7458 6.06066 17.4678C5.87433 17.3993 5.74595 17.3 5.64886 17.1177C4.83662 15.5929 4.01663 14.0722 3.1903 12.555C3.07318 12.34 3.069 12.1596 3.15412 11.9258C3.48081 11.0285 3.74959 10.1127 4.01838 9.19681C4.13973 8.78333 4.26108 8.36985 4.38776 7.95805C4.58101 7.32987 4.7677 6.69948 4.94401 6.06638C4.99443 5.88534 5.08556 5.75256 5.22858 5.63759C5.39411 5.50454 5.56005 5.37195 5.726 5.23937C6.29488 4.78484 6.86382 4.33027 7.41627 3.85651C7.69443 3.61797 8.0152 3.5714 8.33757 3.5246C8.39158 3.51676 8.44564 3.50892 8.49955 3.50016C10.1662 3.22937 11.8339 2.96386 13.5039 2.7149C13.8187 2.66798 13.9158 2.52532 13.92 2.2288C13.9308 1.469 13.9516 1.45701 14.5957 1.85268C16.4284 2.9785 18.2592 4.10762 20.0839 5.24627C20.3206 5.39396 20.4525 5.38951 20.6268 5.15238C20.8508 4.84742 21.0867 4.55117 21.3226 4.25491C21.5261 3.99932 21.7296 3.74373 21.9256 3.4825C22.0803 3.27622 22.2699 3.18017 22.5095 3.13808C22.6331 3.11637 22.7567 3.09483 22.8803 3.0733C23.4039 2.98209 23.9276 2.89086 24.4485 2.78618C24.7832 2.71893 24.9723 2.79198 25.0555 3.15756C25.1046 3.37297 25.1759 3.58326 25.2473 3.79359C25.3157 3.99533 25.3842 4.1971 25.433 4.40346C25.5088 4.72405 25.6547 4.81053 25.9725 4.75395C27.0464 4.56277 28.1234 4.38799 29.2016 4.22168C29.2478 4.21455 29.2975 4.20043 29.3483 4.18602C29.5113 4.13978 29.6851 4.09046 29.7876 4.25873C29.8747 4.4017 29.7714 4.54072 29.6743 4.67136C29.6443 4.7117 29.6149 4.75125 29.5918 4.78986C29.3453 5.20364 29.0915 5.61492 28.8115 6.0063C28.6444 6.23982 28.6828 6.37893 28.8823 6.5669C29.2023 6.86831 29.5156 7.17729 29.8289 7.48621C30.4122 8.06132 30.9953 8.63625 31.6201 9.16183C32.1809 9.63357 32.3403 10.1758 32.3754 10.8382C32.397 11.2464 32.3488 11.6188 32.1631 11.9939C31.7671 12.794 31.4021 13.6097 31.0394 14.4257C30.9247 14.6836 30.7914 14.766 30.5245 14.6322C30.307 14.5232 30.081 14.4309 29.8552 14.3387C29.787 14.3109 29.7189 14.2831 29.6511 14.2548C29.484 14.1852 29.43 14.0811 29.4707 13.8946C29.6108 13.2531 29.7377 12.6087 29.8626 11.964C29.8912 11.8164 29.9698 11.7075 30.0654 11.6017C30.1779 11.4772 30.2888 11.3514 30.4197 11.2029C30.48 11.1345 30.5445 11.0612 30.6154 10.9811C30.376 10.9178 30.1518 10.8123 29.9317 10.7087C29.4153 10.4657 28.9218 10.2335 28.3091 10.5816C28.2585 10.6103 28.203 10.6302 28.1475 10.6501C28.0916 10.6701 28.0358 10.6901 27.985 10.7191C27.2071 11.1631 26.5322 10.9554 25.9068 10.4028C25.8807 10.3796 25.8466 10.3655 25.803 10.3474C25.7794 10.3376 25.7531 10.3267 25.7238 10.3126C25.5289 10.7339 25.4304 11.167 25.3441 11.5967C25.3151 11.741 25.4413 11.8292 25.5599 11.9121C25.5963 11.9375 25.6321 11.9625 25.6624 11.9885C26.7495 12.92 27.8392 13.8485 28.9289 14.777C29.3066 15.0988 29.6842 15.4206 30.0618 15.7425C30.1674 15.8326 30.2732 15.9226 30.379 16.0126C30.7299 16.3111 31.0808 16.6096 31.4278 16.9125C31.8701 17.2986 31.7687 17.9797 31.2417 18.1977C30.9459 18.32 30.6499 18.4419 30.3539 18.5639C29.607 18.8716 28.86 19.1793 28.1157 19.4934C27.8662 19.5987 27.6339 19.6265 27.3661 19.5567C26.9788 19.4559 26.5891 19.3642 26.1994 19.2724C25.8465 19.1894 25.4936 19.1064 25.1424 19.0165C24.9176 18.959 24.7167 18.9706 24.5424 19.1106C23.8549 19.6627 23.211 19.6514 22.497 19.1026C22.0933 18.7924 21.7164 18.5008 21.4278 18.0677C19.9925 15.9141 18.5434 13.7697 17.0943 11.6253C16.7795 11.1595 16.4647 10.6937 16.1501 10.2278C16.1344 10.2046 16.1195 10.1804 16.1045 10.156C16.0403 10.0516 15.9743 9.94422 15.8364 9.89889C15.6477 9.99486 15.5281 10.1605 15.4097 10.3244C15.3781 10.3681 15.3466 10.4117 15.3139 10.4539C15.1804 10.6259 15.0285 10.6769 14.8242 10.645C14.5241 10.598 14.2237 10.5529 13.9232 10.5078C13.2842 10.4118 12.6452 10.3159 12.0093 10.202C11.7264 10.1513 11.4918 10.1777 11.2912 10.3649C10.8675 10.76 10.3713 10.752 9.8414 10.6931C9.74881 10.6829 9.65597 10.6703 9.56294 10.6578C9.24378 10.6147 8.92234 10.5713 8.60109 10.6176C8.56563 10.796 8.6615 10.8756 8.74916 10.9485C8.76697 10.9633 8.78444 10.9778 8.80041 10.9928C9.10816 11.2822 9.41838 11.5689 9.72861 11.8557C9.99674 12.1035 10.2649 12.3514 10.5314 12.6009C10.6696 12.7303 10.8279 12.7818 11.0101 12.7903C11.3457 12.8058 11.6813 12.822 12.0169 12.8381C12.7253 12.8721 13.4337 12.9061 14.1423 12.935C14.361 12.9439 14.5444 13.0141 14.7394 13.11C15.8122 13.6371 16.6313 14.4322 17.2963 15.4137C17.4968 15.7095 17.7216 15.9886 17.9466 16.2679C18.0687 16.4194 18.1907 16.571 18.309 16.7252C18.5697 17.0654 18.5234 17.2198 18.1231 17.3206C17.1786 17.5584 16.2339 17.7954 15.2892 18.0324C13.8312 18.3981 12.3732 18.7638 10.916 19.1328C10.7568 19.1731 10.6202 19.164 10.473 19.1065C10.2361 19.014 9.99712 18.9267 9.74484 18.8346C9.68261 18.8119 9.61958 18.7888 9.55556 18.7654Z"
              fill="white"
            />
            <path
              d="M23.4111 21.8196C23.6012 21.8656 23.7882 21.9118 23.9732 21.9575C24.3591 22.0528 24.7363 22.146 25.1154 22.2306C25.3099 22.274 25.4595 22.3592 25.5884 22.5079C25.6014 22.5229 25.6144 22.5379 25.6274 22.5529C26.0836 23.0795 26.54 23.6062 27.0091 24.1213C27.1609 24.288 27.207 24.4412 27.137 24.6588C26.95 25.2395 26.7742 25.8241 26.6094 26.4114C26.5569 26.5981 26.4524 26.7094 26.2918 26.8055C24.0945 28.1199 21.8975 29.4349 19.7078 30.7621C19.3907 30.9543 19.1762 30.9446 18.9225 30.6522C18.2182 29.8404 17.4985 29.0418 16.7789 28.2434C16.6412 28.0906 16.5035 27.9379 16.366 27.785C16.2329 27.6372 16.1666 27.4826 16.1697 27.2789C16.1795 26.6329 16.1798 25.9865 16.1697 25.3404C16.1664 25.1275 16.2282 24.9572 16.3571 24.7913C17.2941 23.5853 18.2307 22.379 19.1549 21.1633C19.3419 20.9174 19.5533 20.8677 19.8302 20.9366C21.0138 21.2311 22.1983 21.522 23.4111 21.8196Z"
              fill="white"
            />
            <path
              d="M27.8991 27.9604C28.089 27.8088 28.2571 27.8272 28.4452 27.9168C29.2193 28.2856 29.9967 28.6473 30.7757 29.0057C30.9258 29.0748 31.0071 29.1666 31.052 29.3347C31.364 30.5018 31.6884 31.6656 32.0133 32.8292C32.09 33.1041 32.0356 33.3271 31.8326 33.5354C30.8076 34.587 29.7855 35.6416 28.7732 36.7055C28.6024 36.885 28.4347 36.8701 28.2331 36.8181C27.9008 36.7323 27.5677 36.6491 27.2347 36.566C26.5321 36.3906 25.8296 36.2152 25.1343 36.0149C24.5612 35.8498 24.0113 35.7985 23.45 36.0118C23.1239 36.1358 22.966 36.0225 22.8647 35.6894C22.5541 34.6678 22.2249 33.6513 21.8792 32.641C21.7663 32.3111 21.8561 32.1221 22.1302 31.9345C23.5792 30.9431 25.024 29.9455 26.4781 28.9413C26.9506 28.615 27.4241 28.288 27.8991 27.9604Z"
              fill="white"
            />
            <path
              d="M32.7428 20.6878C32.8112 21.621 32.876 22.5214 32.9393 23.422C32.9545 23.6388 32.9689 23.8556 32.9833 24.0724C33.0085 24.4522 33.0337 24.832 33.0634 25.2115C33.083 25.4615 33.0719 25.6983 32.9616 25.9321C32.7111 26.463 32.4679 26.9976 32.2342 27.5361C32.1051 27.8335 31.9218 27.8577 31.6812 27.6829C31.353 27.4445 31.0252 27.2054 30.6975 26.9663C30.2092 26.6102 29.721 26.254 29.2312 25.9001C29.0692 25.783 29.0292 25.6272 29.0207 25.4423C28.9909 24.7977 28.958 24.1532 28.9171 23.5092C28.9072 23.354 28.9632 23.2394 29.0554 23.1257C29.738 22.2837 30.4199 21.4409 31.1017 20.5982C31.4467 20.1717 31.7918 19.7452 32.137 19.3188C32.1389 19.3164 32.1409 19.3139 32.1429 19.3115C32.2385 19.1932 32.3415 19.0658 32.5155 19.1316C32.6535 19.1837 32.6562 19.3076 32.6588 19.4279C32.6593 19.4512 32.6598 19.4745 32.6613 19.497C32.6868 19.883 32.7127 20.2691 32.7428 20.6878Z"
              fill="white"
            />
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M8.3342 18.311C7.57197 18.027 6.81692 17.7458 6.06066 17.4678C5.87433 17.3993 5.74595 17.3 5.64886 17.1177C4.83662 15.5929 4.01663 14.0722 3.1903 12.555C3.07318 12.34 3.069 12.1596 3.15412 11.9258C3.48081 11.0285 3.74959 10.1127 4.01838 9.19681C4.13973 8.78333 4.26108 8.36985 4.38776 7.95805C4.58101 7.32987 4.7677 6.69948 4.94401 6.06638C4.99443 5.88534 5.08556 5.75256 5.22858 5.63759C5.39411 5.50454 5.56005 5.37195 5.726 5.23937C6.29488 4.78484 6.86382 4.33027 7.41627 3.85651C7.69443 3.61797 8.0152 3.5714 8.33757 3.5246C8.39158 3.51676 8.44564 3.50892 8.49955 3.50016C10.1662 3.22937 11.8339 2.96386 13.5039 2.7149C13.8187 2.66798 13.9158 2.52532 13.92 2.2288C13.9308 1.469 13.9516 1.45701 14.5957 1.85268C16.4284 2.9785 18.2592 4.10762 20.0839 5.24627C20.3206 5.39396 20.4525 5.38951 20.6268 5.15238C20.8508 4.84742 21.0867 4.55117 21.3226 4.25491C21.5261 3.99932 21.7296 3.74373 21.9256 3.4825C22.0803 3.27622 22.2699 3.18017 22.5095 3.13808C22.6331 3.11637 22.7567 3.09483 22.8803 3.0733C23.4039 2.98209 23.9276 2.89086 24.4485 2.78618C24.7832 2.71893 24.9723 2.79198 25.0555 3.15756C25.1046 3.37297 25.1759 3.58326 25.2473 3.79359C25.3157 3.99533 25.3842 4.1971 25.433 4.40346C25.5088 4.72405 25.6547 4.81053 25.9725 4.75395C27.0464 4.56277 28.1234 4.38799 29.2016 4.22168C29.2478 4.21455 29.2975 4.20043 29.3483 4.18602C29.5113 4.13978 29.6851 4.09046 29.7876 4.25873C29.8747 4.4017 29.7714 4.54072 29.6743 4.67136C29.6443 4.7117 29.6149 4.75125 29.5918 4.78986C29.3453 5.20364 29.0915 5.61492 28.8115 6.0063C28.6444 6.23982 28.6828 6.37893 28.8823 6.5669C29.2023 6.86831 29.5156 7.17729 29.8289 7.48621C30.4122 8.06132 30.9953 8.63625 31.6201 9.16183C32.1809 9.63357 32.3403 10.1758 32.3754 10.8382C32.397 11.2464 32.3488 11.6188 32.1631 11.9939C31.7671 12.794 31.4021 13.6097 31.0394 14.4257C30.9247 14.6836 30.7914 14.766 30.5245 14.6322C30.307 14.5232 30.081 14.4309 29.8552 14.3387C29.787 14.3109 29.7189 14.2831 29.6511 14.2548C29.484 14.1852 29.43 14.0811 29.4707 13.8946C29.6108 13.2531 29.7377 12.6087 29.8626 11.964C29.8912 11.8164 29.9698 11.7075 30.0654 11.6017C30.1779 11.4772 30.2888 11.3514 30.4197 11.2029C30.48 11.1345 30.5445 11.0612 30.6154 10.9811C30.376 10.9178 30.1518 10.8123 29.9317 10.7087C29.4153 10.4657 28.9218 10.2335 28.3091 10.5816C28.2585 10.6103 28.203 10.6302 28.1475 10.6501C28.0916 10.6701 28.0358 10.6901 27.985 10.7191C27.2071 11.1631 26.5322 10.9554 25.9068 10.4028C25.8807 10.3796 25.8466 10.3655 25.803 10.3474C25.7794 10.3376 25.7531 10.3267 25.7238 10.3126C25.5289 10.7339 25.4304 11.167 25.3441 11.5967C25.3151 11.741 25.4413 11.8292 25.5599 11.9121C25.5963 11.9375 25.6321 11.9625 25.6624 11.9885C26.7495 12.92 27.8392 13.8485 28.9289 14.777C29.3066 15.0988 29.6842 15.4206 30.0618 15.7425C30.1674 15.8326 30.2732 15.9226 30.379 16.0126C30.7299 16.3111 31.0808 16.6096 31.4278 16.9125C31.8701 17.2986 31.7687 17.9797 31.2417 18.1977C30.9459 18.32 30.6499 18.4419 30.3539 18.5639C29.607 18.8716 28.86 19.1793 28.1157 19.4934C27.8662 19.5987 27.6339 19.6265 27.3661 19.5567C26.9788 19.4559 26.5891 19.3642 26.1994 19.2724C25.8465 19.1894 25.4936 19.1064 25.1424 19.0165C24.9176 18.959 24.7167 18.9706 24.5424 19.1106C23.8549 19.6627 23.211 19.6514 22.497 19.1026C22.0933 18.7924 21.7164 18.5008 21.4278 18.0677C19.9925 15.9141 18.5434 13.7697 17.0943 11.6253C16.7795 11.1595 16.4647 10.6937 16.1501 10.2278C16.1344 10.2046 16.1195 10.1804 16.1045 10.156C16.0403 10.0516 15.9743 9.94422 15.8364 9.89889C15.6477 9.99486 15.5281 10.1605 15.4097 10.3244C15.3781 10.3681 15.3466 10.4117 15.3139 10.4539C15.1804 10.6259 15.0285 10.6769 14.8242 10.645C14.5241 10.598 14.2237 10.5529 13.9232 10.5078C13.2842 10.4118 12.6452 10.3159 12.0093 10.202C11.7264 10.1513 11.4918 10.1777 11.2912 10.3649C10.8675 10.76 10.3713 10.752 9.8414 10.6931C9.74881 10.6829 9.65597 10.6703 9.56294 10.6578C9.24378 10.6147 8.92234 10.5713 8.60109 10.6176C8.56563 10.796 8.6615 10.8756 8.74916 10.9485C8.76697 10.9633 8.78444 10.9778 8.80041 10.9928C9.10816 11.2822 9.41838 11.5689 9.72861 11.8557C9.99674 12.1035 10.2649 12.3514 10.5314 12.6009C10.6696 12.7303 10.8279 12.7818 11.0101 12.7903C11.3457 12.8058 11.6813 12.822 12.0169 12.8381C12.7253 12.8721 13.4337 12.9061 14.1423 12.935C14.361 12.9439 14.5444 13.0141 14.7394 13.11C15.8122 13.6371 16.6313 14.4322 17.2963 15.4137C17.4968 15.7095 17.7216 15.9886 17.9466 16.2679C18.0687 16.4194 18.1907 16.571 18.309 16.7252C18.5697 17.0654 18.5234 17.2198 18.1231 17.3206C17.1786 17.5584 16.2339 17.7954 15.2892 18.0324C13.8312 18.3981 12.3732 18.7638 10.916 19.1328C10.7568 19.1731 10.6202 19.164 10.473 19.1065C10.2361 19.014 9.99712 18.9267 9.74484 18.8346C9.68261 18.8119 9.61958 18.7888 9.55556 18.7654ZM23.9732 21.9575C24.3591 22.0528 24.7363 22.146 25.1154 22.2306C25.3099 22.274 25.4595 22.3592 25.5884 22.5079L25.6274 22.5529C26.0836 23.0795 26.54 23.6062 27.0091 24.1213C27.1609 24.288 27.207 24.4412 27.137 24.6588C26.95 25.2395 26.7742 25.8241 26.6094 26.4114C26.5569 26.5981 26.4524 26.7094 26.2918 26.8055C24.0945 28.1199 21.8975 29.4349 19.7078 30.7621C19.3907 30.9543 19.1762 30.9446 18.9225 30.6522C18.2182 29.8404 17.4985 29.0418 16.7789 28.2434C16.6412 28.0906 16.5035 27.9379 16.366 27.785C16.2329 27.6372 16.1666 27.4826 16.1697 27.2789C16.1795 26.6329 16.1798 25.9865 16.1697 25.3404C16.1664 25.1275 16.2282 24.9572 16.3571 24.7913C17.2941 23.5853 18.2307 22.379 19.1549 21.1633C19.3419 20.9174 19.5533 20.8677 19.8302 20.9366C21.0138 21.2311 22.1983 21.522 23.4111 21.8196ZM27.8991 27.9604C28.089 27.8088 28.2571 27.8272 28.4452 27.9168C29.2193 28.2856 29.9967 28.6473 30.7757 29.0057C30.9258 29.0748 31.0071 29.1666 31.052 29.3347C31.364 30.5018 31.6884 31.6656 32.0133 32.8292C32.09 33.1041 32.0356 33.3271 31.8326 33.5354C30.8076 34.587 29.7855 35.6416 28.7732 36.7055C28.6024 36.885 28.4347 36.8701 28.2331 36.8181C27.9008 36.7323 27.5677 36.6491 27.2347 36.566C26.5321 36.3906 25.8296 36.2152 25.1343 36.0149C24.5612 35.8498 24.0113 35.7985 23.45 36.0118C23.1239 36.1358 22.966 36.0225 22.8647 35.6894C22.5541 34.6678 22.2249 33.6513 21.8792 32.641C21.7663 32.3111 21.8561 32.1221 22.1302 31.9345C23.5792 30.9431 25.024 29.9455 26.4781 28.9413C26.9506 28.615 27.4241 28.288 27.8991 27.9604ZM32.7428 20.6878C32.8112 21.621 32.876 22.5214 32.9393 23.422C32.9545 23.6388 32.9689 23.8556 32.9833 24.0724C33.0085 24.4522 33.0337 24.832 33.0634 25.2115C33.083 25.4615 33.0719 25.6983 32.9616 25.9321C32.7111 26.463 32.4679 26.9976 32.2342 27.5361C32.1051 27.8335 31.9218 27.8577 31.6812 27.6829C31.353 27.4445 31.0252 27.2054 30.6975 26.9663C30.2092 26.6102 29.721 26.254 29.2312 25.9001C29.0692 25.783 29.0292 25.6272 29.0207 25.4423C28.9909 24.7977 28.958 24.1532 28.9171 23.5092C28.9072 23.354 28.9632 23.2394 29.0554 23.1257C29.738 22.2837 30.4199 21.4409 31.1017 20.5982C31.4467 20.1717 31.7918 19.7452 32.137 19.3188L32.1429 19.3115C32.2385 19.1932 32.3415 19.0658 32.5155 19.1316C32.6535 19.1837 32.6562 19.3076 32.6588 19.4279C32.6593 19.4512 32.6598 19.4745 32.6613 19.497C32.6868 19.883 32.7127 20.2691 32.7428 20.6878Z"
              fill="white"
            />
          </svg>
        </a>
      </div>
      <div class="cta">
        <a href="/kodex/registration">
          <button
            class="border font-[urbanians] tracking-[.4px] mt-2.5 flex gap-9 border-white/[0.1] transition-all ease duration-500 bg-grad-hover text-[.72rem] bg-grad inner-shadow px-[2rem] font-thin py-[.58rem] rounded-full text-white/50 transition-all relative z-[9999999]"
          >
            Enroll Now
          </button>
        </a>
      </div>
    </nav>
    <div class="ranks">
      <div
        class="h-full mt-44 lg:mt-32 w-full flex flex-col justify-center pl-[2rem] lg:pl-[3.7rem] pr-[1.5rem] md:pr-[4.1rem] pt-[2.7rem]"
      >
        <div
          class="flex flex-col md:flex-row gap-10 mt-10 justify-between w-full"
        >
          <h1 class="text-white text-3xl font-semibold max-w-xl">
            Increase Your Chance to Secure a Top Rank and unlock Codex through a
            test.
          </h1>
          <div class="button">
            <button
              class="bg-[#525252] start-btn px-10 py-1.5 rounded-full text-white"
            >
              Next
            </button>
          </div>
        </div>
        <div class="w-full border border-white/30 p-3 md:p-7 rounded-3xl mt-10">
          <% if(myRankings) { %>
          <div class="flex justify-between items-start">
            <div class="text-white">
              <h1 class="text-lg sm:text-2xl mb-1">Ranking</h1>
              <p class="text-xs sm:w-auto w-44 sm:text-sm">
                You have secured
                <span class="font-bold"><%= myRankings?.rank %></span> rank
                among
                <span class="font-bold"><%= registrations.length + 100 %></span>
                students.
              </p>
            </div>
            <button
              class="border border-white/30 px-3 py-0.5 sm:py-1 text-xs sm:text-sm rounded-full text-white"
            >
              <%= registrations.length %> students
            </button>
          </div>
          <%} %>
          <div class="table w-full mt-5">
            <div
              class="flex justify-between text-white px-3 sm:px-5 py-3 mb-4 rounded-full uppercase bg-[#525252] w-full"
            >
              <h1 class="font-bold text-xs sm:text-base tracking-2">Ranking</h1>

              <h1 class="font-bold text-xs sm:text-base tracking-2">
                Students Participated
              </h1>
              <h1 class="font-bold text-xs sm:text-base tracking-2">Mark</h1>
            </div>
            <div
              class="flex-col overflow-y-auto flex gap-2 mt-2 custom-scrollbar"
            >
              <% if(myRankings) { %>
              <div
                data-user-id="<%= myRankings.userId %>"
                class="flex justify-between bg-[#272727] sticky top-0 z-[2] items-center px-5 py-3 rounded-full bg-[#2B2B2B] text-white border border-white/30 w-full"
              >
                <h1 class="font-bold tracking-2 relative">
                  <%= myRankings?.rank %> <% if (myRankings?.rank === 1) { %>
                  <svg
                    class="absolute sm:scale-100 scale-50 -top-1.5 left-7"
                    width="31"
                    height="34"
                    viewBox="0 0 31 34"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M29.1429 4.85714H24.2857V2.42857C24.9564 2.42857 25.5 1.88494 25.5 1.21429C25.5 0.543636 24.9564 0 24.2857 0H6.07143C5.40078 0 4.85714 0.543636 4.85714 1.21429C4.85714 1.88494 5.40078 2.42857 6.07143 2.42857V4.85714H1.21429C0.546429 4.85714 0 5.40357 0 6.07143V7.28571C0 11.5661 2.91429 15.3 7.06714 16.3382L7.51036 16.4475H7.51643C8.80946 18.469 10.8856 19.9442 13.3198 20.4544C13.204 22.1921 12.7975 23.8984 12.112 25.5H9.10714C8.43929 25.5 7.89286 26.0464 7.89286 26.7143V31.5714C7.22221 31.5714 6.67857 32.1151 6.67857 32.7857V34H23.6786V32.7857C23.6786 32.1151 23.1349 31.5714 22.4643 31.5714V26.7143C22.4643 26.0464 21.9179 25.5 21.25 25.5H18.2451C17.5597 23.8984 17.1532 22.1921 17.0373 20.4544C19.4715 19.9442 21.5477 18.469 22.8407 16.4475H22.8468L23.29 16.3382C27.4429 15.3 30.3571 11.5661 30.3571 7.28571V6.07143C30.3571 5.40357 29.8107 4.85714 29.1429 4.85714ZM18.2143 28.5357C18.2143 28.8696 17.9411 29.1429 17.6071 29.1429H12.75C12.4161 29.1429 12.1429 28.8696 12.1429 28.5357C12.1429 28.2018 12.4161 27.9286 12.75 27.9286H17.6071C17.9411 27.9286 18.2143 28.2018 18.2143 28.5357ZM2.42857 7.28571H6.07143V11.5357C6.07143 12.2036 6.14429 12.8471 6.27786 13.4725C3.95857 12.3371 2.42857 9.95714 2.42857 7.28571ZM17.4311 13.7882L15.1786 12.6043L12.9261 13.7882L13.3571 14.3185L14.6112 13.4714L15.8653 14.3185L16.2964 13.7882H17.4311Z"
                      fill="white"
                    />
                  </svg>
                  <% } %>
                </h1>
                <div class="flex gap-3 sm:w-60 ml-4 items-center">
                  <% if (myRankings?.avatar) { %>
                  <div
                    class="h-10 w-10 bg-blue-400 overflow-hidden sm:flex hidden rounded-full items-center justify-center text-lg font-bold"
                  >
                    <img
                      class="h-full w-full object-cover"
                      src="<%= myRankings.avatar %>"
                      alt=""
                    />
                  </div>
                  <% } %>
                  <h1 class="font-bold tracking-2">
                    <%= myRankings?.username %>
                  </h1>
                </div>
                <h1 class="font-bold tracking-2"><%= myRankings?.score %></h1>
              </div>
              <% } %> <% (Array.isArray(registrations) ? registrations :
              []).forEach(function(user, index) { %> <% let rank = index + 1; %>
              <%if(user?._id?.toString() !== myRankings?._id?.toString()) { %>
              <div
                data-user-id="<%= user._id %>"
                class="flex justify-between items-center px-5 py-3 rounded-full bg-[#131313] text-white border border-white/30 w-full"
              >
                <h1 class="font-bold sm:text-xl tracking-2 relative">
                  <%= rank %> <% if (rank === 1) { %>
                  <svg
                    class="absolute sm:scale-100 scale-50 -top-0.5 left-7"
                    width="31"
                    height="31"
                    viewBox="0 0 31 34"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M29.1429 4.85714H24.2857V2.42857C24.9564 2.42857 25.5 1.88494 25.5 1.21429C25.5 0.543636 24.9564 0 24.2857 0H6.07143C5.40078 0 4.85714 0.543636 4.85714 1.21429C4.85714 1.88494 5.40078 2.42857 6.07143 2.42857V4.85714H1.21429C0.546429 4.85714 0 5.40357 0 6.07143V7.28571C0 11.5661 2.91429 15.3 7.06714 16.3382L7.51036 16.4475H7.51643C8.80946 18.469 10.8856 19.9442 13.3198 20.4544C13.204 22.1921 12.7975 23.8984 12.112 25.5H9.10714C8.43929 25.5 7.89286 26.0464 7.89286 26.7143V31.5714C7.22221 31.5714 6.67857 32.1151 6.67857 32.7857V34H23.6786V32.7857C23.6786 32.1151 23.1349 31.5714 22.4643 31.5714V26.7143C22.4643 26.0464 21.9179 25.5 21.25 25.5H18.2451C17.5597 23.8984 17.1532 22.1921 17.0373 20.4544C19.4715 19.9442 21.5477 18.469 22.8407 16.4475H22.8468L23.29 16.3382C27.4429 15.3 30.3571 11.5661 30.3571 7.28571V6.07143C30.3571 5.40357 29.8107 4.85714 29.1429 4.85714ZM18.2143 28.5357C18.2143 28.8696 17.9411 29.1429 17.6071 29.1429H12.75C12.4161 29.1429 12.1429 28.8696 12.1429 28.5357C12.1429 28.2018 12.4161 27.9286 12.75 27.9286H17.6071C17.9411 27.9286 18.2143 28.2018 18.2143 28.5357ZM2.42857 7.28571H6.07143V11.5357C6.07143 12.2036 6.14429 12.8471 6.27786 13.4725C3.95857 12.3371 2.42857 9.95714 2.42857 7.28571ZM17.4311 13.7882L15.1786 12.6043L12.9261 13.7882L13.3571 11.2807L11.5357 9.50179L14.0554 9.1375L15.1786 6.85464L16.3018 9.1375L18.8214 9.50179L17 11.2807L17.4311 13.7882ZM24.0793 13.4725C24.2129 12.8471 24.2857 12.2036 24.2857 11.5357V7.28571H27.9286C27.9286 9.95714 26.3986 12.3371 24.0793 13.4725Z"
                      fill="white"
                    />
                  </svg>
                  <% } %>
                </h1>

                <div class="flex gap-3 text-left sm:w-60 ml-10 items-center">
                  <% if (user?.avatar) { %>
                  <div
                    class="h-10 w-10 bg-blue-400 overflow-hidden sm:flex hidden rounded-full items-center justify-center text-lg font-bold"
                  >
                    <img
                      class="h-full w-full object-cover"
                      src="<%= user.avatar %>"
                      alt=""
                    />
                  </div>
                  <%}%>
                  <h1 class="font-bold sm:text-xl tracking-2">
                    <%= user.username %>
                  </h1>
                </div>
                <h1 class="font-bold sm:text-xl tracking-2">
                  <%= user.score %>
                </h1>
              </div>
              <%}%> <% }); %>
            </div>
          </div>
        </div>
      </div>

      <div
        id="rank-improvement-popup"
        class="fixed bottom-0 w-[90%] sm:max-w-lg z-[9999999999999] left-1/2 -translate-x-1/2 bottom-10 right-0 bg-gradient-to-r from-[#303030] to-black border-t border-white/10 p-4 transform transition-transform duration-500 rounded-lg flex justify-between items-center z-50"
      >
        <div class="text-white">
          <h3 class="sm:text-xl font-medium">Want to improve your rank?</h3>
          <p class="text-xs sm:text-sm text-gray-300">
            Take a quick test to boost your position on the leaderboard.
          </p>
        </div>
        <div class="flex sm:flex-row flex-col-reverse gap-3">
          <button
            id="skip-btn"
            class="px-6 py-2 bg-transparent border border-white/30 text-white/50 rounded-full hover:bg-white/10 transition-all"
          >
            Skip
          </button>
          <button
            class="border start-btn border-white/[0.1] transition-all ease duration-500 bg-grad-hover bg-grad inner-shadow px-7 font-thin py-1.5 rounded-full text-white"
          >
            Start
          </button>
        </div>
      </div>
    </div>

    <div
      class="navigate-popup hidden flex items-center justify-center relative z-0 h-screen w-full"
    >
      <div class="absolute right-0 top-[25%] z-[-1]">
        <svg
          width="158"
          height="209"
          viewBox="0 0 158 209"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g filter="url(#filter0_ddddi_907_1397)">
            <rect
              x="14"
              y="69.1801"
              width="126.132"
              height="126.132"
              rx="18.9198"
              transform="rotate(-17.6199 14 69.1801)"
              fill="url(#paint0_linear_907_1397)"
            />
            <rect
              x="14.8345"
              y="69.6123"
              width="124.803"
              height="124.803"
              rx="18.2552"
              transform="rotate(-17.6199 14.8345 69.6123)"
              stroke="#282828"
              stroke-width="1.32905"
            />
          </g>
          <g filter="url(#filter1_i_907_1397)">
            <path
              d="M115.41 118.42L122.913 103.049L107.542 95.5461M73.2304 107.348L65.7272 122.719L81.0982 130.222M93.6003 87.5512L95.0396 138.217"
              stroke="url(#paint1_linear_907_1397)"
              stroke-width="6.04738"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </g>
          <defs>
            <filter
              id="filter0_ddddi_907_1397"
              x="0.391126"
              y="0.273311"
              width="200.027"
              height="207.912"
              filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB"
            >
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feMorphology
                radius="17.2776"
                operator="erode"
                in="SourceAlpha"
                result="effect1_dropShadow_907_1397"
              />
              <feOffset dx="-11.7122" dy="21.6226" />
              <feGaussianBlur stdDeviation="9.64007" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"
              />
              <feBlend
                mode="normal"
                in2="BackgroundImageFix"
                result="effect1_dropShadow_907_1397"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset dy="3.60377" />
              <feGaussianBlur stdDeviation="7.04396" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"
              />
              <feBlend
                mode="normal"
                in2="effect1_dropShadow_907_1397"
                result="effect2_dropShadow_907_1397"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feMorphology
                radius="26.581"
                operator="erode"
                in="SourceAlpha"
                result="effect3_dropShadow_907_1397"
              />
              <feOffset dx="7.20753" dy="-9.91035" />
              <feGaussianBlur stdDeviation="26.1158" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"
              />
              <feBlend
                mode="normal"
                in2="effect2_dropShadow_907_1397"
                result="effect3_dropShadow_907_1397"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feMorphology
                radius="2.70282"
                operator="erode"
                in="SourceAlpha"
                result="effect4_dropShadow_907_1397"
              />
              <feOffset dx="0.900941" dy="-7.20753" />
              <feGaussianBlur stdDeviation="2.43254" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.46 0"
              />
              <feBlend
                mode="normal"
                in2="effect3_dropShadow_907_1397"
                result="effect4_dropShadow_907_1397"
              />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="effect4_dropShadow_907_1397"
                result="shape"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset dy="5.31619" />
              <feGaussianBlur stdDeviation="2.45874" />
              <feComposite
                in2="hardAlpha"
                operator="arithmetic"
                k2="-1"
                k3="1"
              />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0.634615 0 0 0 0 0.634615 0 0 0 0 0.634615 0 0 0 0.25 0"
              />
              <feBlend
                mode="normal"
                in2="shape"
                result="effect5_innerShadow_907_1397"
              />
            </filter>
            <filter
              id="filter1_i_907_1397"
              x="61.6449"
              y="84.5275"
              width="64.2921"
              height="59.2384"
              filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB"
            >
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="BackgroundImageFix"
                result="shape"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset dx="-1.05793" dy="2.6581" />
              <feGaussianBlur stdDeviation="1.2626" />
              <feComposite
                in2="hardAlpha"
                operator="arithmetic"
                k2="-1"
                k3="1"
              />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0.322115 0 0 0 0 0.322115 0 0 0 0 0.322115 0 0 0 1 0"
              />
              <feBlend
                mode="normal"
                in2="shape"
                result="effect1_innerShadow_907_1397"
              />
            </filter>
            <linearGradient
              id="paint0_linear_907_1397"
              x1="77.0659"
              y1="69.1801"
              x2="77.0659"
              y2="195.312"
              gradientUnits="userSpaceOnUse"
            >
              <stop />
              <stop offset="1" stop-color="#272727" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_907_1397"
              x1="86.4521"
              y1="90.0099"
              x2="102.188"
              y2="135.758"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#A9A9A9" />
            </linearGradient>
          </defs>
        </svg>
      </div>
      <div class="absolute left-0 top-[60%] z-[-1]">
        <svg
          width="100"
          height="162"
          viewBox="0 0 100 162"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g filter="url(#filter0_f_907_1401)">
            <g filter="url(#filter1_ddddi_907_1401)">
              <rect
                x="-44"
                y="53.9972"
                width="97.1503"
                height="97.1503"
                rx="14.5725"
                transform="rotate(-17.6199 -44 53.9972)"
                fill="url(#paint0_linear_907_1401)"
              />
              <rect
                x="-43.3572"
                y="54.3301"
                width="96.1266"
                height="96.1266"
                rx="14.0607"
                transform="rotate(-17.6199 -43.3572 54.3301)"
                stroke="#282828"
                stroke-width="1.02367"
              />
            </g>
            <g filter="url(#filter2_i_907_1401)">
              <path
                d="M34.108 91.923L39.8872 80.0838L28.048 74.3047M1.62045 83.3947L-4.15871 95.2339L7.68049 101.013M17.3099 68.1468L18.4186 107.171"
                stroke="url(#paint1_linear_907_1401)"
                stroke-width="4.65786"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </g>
          </g>
          <defs>
            <filter
              id="filter0_f_907_1401"
              x="-50.8202"
              y="17.7695"
              width="135.64"
              height="135.641"
              filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB"
            >
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="BackgroundImageFix"
                result="shape"
              />
              <feGaussianBlur
                stdDeviation="5.27191"
                result="effect1_foregroundBlur_907_1401"
              />
            </filter>
            <filter
              id="filter1_ddddi_907_1401"
              x="-54.4818"
              y="0.923206"
              width="154.066"
              height="160.14"
              filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB"
            >
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feMorphology
                radius="13.3077"
                operator="erode"
                in="SourceAlpha"
                result="effect1_dropShadow_907_1401"
              />
              <feOffset dx="-9.0211" dy="16.6543" />
              <feGaussianBlur stdDeviation="7.42506" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"
              />
              <feBlend
                mode="normal"
                in2="BackgroundImageFix"
                result="effect1_dropShadow_907_1401"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset dy="2.77572" />
              <feGaussianBlur stdDeviation="5.42546" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"
              />
              <feBlend
                mode="normal"
                in2="effect1_dropShadow_907_1401"
                result="effect2_dropShadow_907_1401"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feMorphology
                radius="20.4734"
                operator="erode"
                in="SourceAlpha"
                result="effect3_dropShadow_907_1401"
              />
              <feOffset dx="5.55145" dy="-7.63324" />
              <feGaussianBlur stdDeviation="20.1151" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"
              />
              <feBlend
                mode="normal"
                in2="effect2_dropShadow_907_1401"
                result="effect3_dropShadow_907_1401"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feMorphology
                radius="2.08179"
                operator="erode"
                in="SourceAlpha"
                result="effect4_dropShadow_907_1401"
              />
              <feOffset dx="0.693931" dy="-5.55145" />
              <feGaussianBlur stdDeviation="1.87361" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.46 0"
              />
              <feBlend
                mode="normal"
                in2="effect3_dropShadow_907_1401"
                result="effect4_dropShadow_907_1401"
              />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="effect4_dropShadow_907_1401"
                result="shape"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset dy="4.09468" />
              <feGaussianBlur stdDeviation="1.89379" />
              <feComposite
                in2="hardAlpha"
                operator="arithmetic"
                k2="-1"
                k3="1"
              />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0.634615 0 0 0 0 0.634615 0 0 0 0 0.634615 0 0 0 0.25 0"
              />
              <feBlend
                mode="normal"
                in2="shape"
                result="effect5_innerShadow_907_1401"
              />
            </filter>
            <filter
              id="filter2_i_907_1401"
              x="-7.30313"
              y="65.8178"
              width="49.5197"
              height="45.6271"
              filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB"
            >
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="BackgroundImageFix"
                result="shape"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset dx="-0.814849" dy="2.04734" />
              <feGaussianBlur stdDeviation="0.972487" />
              <feComposite
                in2="hardAlpha"
                operator="arithmetic"
                k2="-1"
                k3="1"
              />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0.322115 0 0 0 0 0.322115 0 0 0 0 0.322115 0 0 0 1 0"
              />
              <feBlend
                mode="normal"
                in2="shape"
                result="effect1_innerShadow_907_1401"
              />
            </filter>
            <linearGradient
              id="paint0_linear_907_1401"
              x1="4.57515"
              y1="53.9972"
              x2="4.57515"
              y2="151.147"
              gradientUnits="userSpaceOnUse"
            >
              <stop />
              <stop offset="1" stop-color="#272727" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_907_1401"
              x1="11.8042"
              y1="70.0405"
              x2="23.9243"
              y2="105.277"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#A9A9A9" />
            </linearGradient>
          </defs>
        </svg>
      </div>
      <div
        class="md:w-[50%] lg:w-[28rem] relative z-2 sm:w-[60%] w-[90%] text-white text-center flex flex-col gap-3 items-center p-3 sm:p-10 border border-white/20 rounded-[1.5rem] bg-grad-input"
      >
        <svg
          class="absolute top-0 left-0 w-full z-[0] h-full"
          width="497"
          height="303"
          viewBox="0 0 497 303"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M57.0406 236.4C57.1197 236.082 56.9498 235.775 56.7739 235.918C56.7359 235.95 56.7066 236.003 56.689 236.072C56.6069 236.39 56.7798 236.697 56.9556 236.554C56.9937 236.522 57.023 236.469 57.0406 236.4Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M198.052 254.825C198.131 254.507 197.961 254.199 197.784 254.343C197.746 254.374 197.717 254.427 197.699 254.496C197.62 254.815 197.79 255.122 197.967 254.979C198.005 254.947 198.034 254.894 198.052 254.825Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M167.612 224.456C167.691 224.138 167.521 223.83 167.345 223.973C167.307 224.005 167.278 224.058 167.26 224.127C167.178 224.445 167.351 224.753 167.527 224.61C167.565 224.578 167.594 224.525 167.612 224.456Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M126.266 78.5381C126.348 78.2199 126.175 77.9123 125.999 78.0555C125.961 78.0873 125.932 78.1403 125.914 78.2093C125.835 78.5275 126.005 78.8351 126.181 78.6919C126.219 78.6601 126.249 78.607 126.266 78.5381Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M151.764 70.3702C151.846 70.0528 151.673 69.7407 151.497 69.8888C151.459 69.9206 151.43 69.9735 151.412 70.0422C151.333 70.3596 151.503 70.6664 151.679 70.5235C151.717 70.4918 151.746 70.4389 151.764 70.3702Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M449.087 4.77101C449.166 4.45281 448.996 4.1452 448.82 4.2884C448.782 4.32022 448.752 4.37325 448.735 4.4422C448.655 4.76041 448.826 5.06801 449.002 4.92482C449.04 4.893 449.07 4.83996 449.087 4.77101Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M317.574 267.685C317.654 267.366 317.483 267.059 317.307 267.202C317.269 267.234 317.239 267.287 317.222 267.356C317.142 267.674 317.313 267.982 317.489 267.838C317.527 267.807 317.557 267.754 317.574 267.685Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M352.503 285.843C352.585 285.525 352.413 285.217 352.237 285.36C352.199 285.392 352.169 285.445 352.152 285.514C352.073 285.832 352.243 286.14 352.418 285.997C352.457 285.965 352.486 285.912 352.503 285.843Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M424.627 213.396C424.709 213.078 424.537 212.77 424.361 212.913C424.323 212.945 424.293 212.998 424.276 213.067C424.197 213.385 424.367 213.693 424.542 213.55C424.581 213.518 424.61 213.465 424.627 213.396Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M441.284 98.2202C441.366 97.902 441.194 97.5944 441.018 97.7376C440.98 97.7694 440.95 97.8225 440.933 97.8914C440.854 98.2096 441.024 98.5172 441.199 98.374C441.238 98.3422 441.267 98.2892 441.284 98.2202Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M424.273 11.312C424.352 10.9938 424.182 10.6862 424.007 10.8294C423.969 10.8612 423.939 10.9143 423.922 10.9832C423.84 11.3014 424.012 11.609 424.188 11.4658C424.226 11.434 424.256 11.381 424.273 11.312Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M453.796 32.4087C453.876 32.0905 453.705 31.7829 453.529 31.9261C453.491 31.9579 453.461 32.0109 453.444 32.0799C453.365 32.3981 453.535 32.7057 453.711 32.5625C453.749 32.5307 453.779 32.4777 453.796 32.4087Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M421.605 49.416C421.688 49.0978 421.515 48.7902 421.339 48.9334C421.301 48.9652 421.271 49.0183 421.254 49.0872C421.175 49.4054 421.345 49.713 421.52 49.5698C421.559 49.538 421.588 49.485 421.605 49.416Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M422.39 125.243C422.472 124.925 422.299 124.617 422.123 124.761C422.085 124.792 422.056 124.845 422.038 124.914C421.959 125.233 422.129 125.54 422.305 125.397C422.343 125.365 422.372 125.312 422.39 125.243Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M484.418 137.291C484.497 136.972 484.327 136.665 484.151 136.808C484.113 136.84 484.084 136.893 484.066 136.962C483.984 137.28 484.157 137.588 484.333 137.444C484.371 137.413 484.4 137.359 484.418 137.291Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M155.048 143.668C155.127 143.35 154.957 143.043 154.781 143.186C154.743 143.218 154.714 143.271 154.696 143.34C154.614 143.658 154.787 143.965 154.963 143.822C155.001 143.79 155.03 143.737 155.048 143.668Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M254.369 249.259C254.448 248.941 254.278 248.633 254.102 248.777C254.064 248.808 254.034 248.862 254.017 248.93C253.937 249.249 254.108 249.556 254.284 249.413C254.322 249.381 254.352 249.328 254.369 249.259Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M201.129 221.651C201.211 221.333 201.038 221.025 200.863 221.168C200.824 221.2 200.795 221.253 200.778 221.322C200.698 221.64 200.868 221.948 201.044 221.805C201.082 221.773 201.112 221.72 201.129 221.651Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M101.207 263.983C101.286 263.665 101.116 263.357 100.94 263.5C100.902 263.532 100.873 263.585 100.855 263.654C100.773 263.972 100.946 264.28 101.122 264.137C101.16 264.105 101.189 264.052 101.207 263.983Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M337.595 6.188C337.677 5.86979 337.504 5.5622 337.328 5.70539C337.29 5.73721 337.261 5.79024 337.243 5.85919C337.164 6.1774 337.334 6.485 337.51 6.34181C337.548 6.30999 337.577 6.25695 337.595 6.188Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M155.834 287.527C155.913 287.209 155.743 286.902 155.567 287.045C155.529 287.077 155.5 287.13 155.482 287.199C155.4 287.517 155.573 287.824 155.749 287.681C155.787 287.649 155.816 287.596 155.834 287.527Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M104.012 38.7866C104.094 38.4684 103.921 38.1608 103.745 38.304C103.707 38.3358 103.678 38.3889 103.66 38.4578C103.581 38.776 103.751 39.0836 103.927 38.9404C103.965 38.9086 103.994 38.8556 104.012 38.7866Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M152.299 12.5659C152.378 12.2477 152.209 11.9401 152.033 12.0833C151.995 12.1151 151.965 12.1682 151.948 12.2371C151.866 12.5553 152.039 12.8629 152.214 12.7197C152.252 12.6879 152.282 12.6349 152.299 12.5659Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M38.8452 197.527C38.9245 197.209 38.7541 196.901 38.5779 197.044C38.5397 197.076 38.5103 197.129 38.4927 197.198C38.4133 197.516 38.5837 197.824 38.76 197.681C38.7982 197.649 38.8276 197.596 38.8452 197.527Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M62.2495 183.457C62.3315 183.139 62.1587 182.831 61.9829 182.974C61.9448 183.006 61.9155 183.059 61.8979 183.128C61.8188 183.446 61.9887 183.754 62.1645 183.611C62.2026 183.579 62.2319 183.526 62.2495 183.457Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M65.54 101.149C65.6194 100.831 65.449 100.523 65.2727 100.666C65.2345 100.698 65.2051 100.751 65.1875 100.82C65.1082 101.138 65.2786 101.446 65.4548 101.303C65.493 101.271 65.5224 101.218 65.54 101.149Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M62.7933 138.708C62.8724 138.39 62.7024 138.082 62.5266 138.225C62.4885 138.257 62.4592 138.31 62.4417 138.379C62.3596 138.697 62.5325 139.005 62.7083 138.862C62.7464 138.83 62.7757 138.777 62.7933 138.708Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M98.0838 290.488C98.1629 290.17 97.993 289.862 97.8172 290.005C97.7791 290.037 97.7498 290.09 97.7322 290.159C97.6501 290.477 97.823 290.785 97.9988 290.642C98.0369 290.61 98.0662 290.557 98.0838 290.488Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M151.628 184.58C151.708 184.261 151.537 183.954 151.361 184.097C151.323 184.129 151.293 184.182 151.276 184.251C151.197 184.569 151.367 184.877 151.543 184.733C151.581 184.702 151.611 184.649 151.628 184.58Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M138.434 156.16C138.513 155.842 138.343 155.534 138.167 155.678C138.129 155.709 138.099 155.762 138.082 155.831C138.002 156.15 138.173 156.457 138.349 156.314C138.387 156.282 138.416 156.229 138.434 156.16Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M86.0788 104.137C86.2551 104.28 86.4255 103.973 86.3462 103.656C86.3286 103.587 86.2992 103.534 86.261 103.503C86.0847 103.354 85.9143 103.667 85.9936 103.984C86.0113 104.053 86.0407 104.106 86.0788 104.137Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M102.598 210.666C102.677 210.347 102.507 210.04 102.331 210.183C102.293 210.215 102.264 210.268 102.246 210.337C102.164 210.655 102.337 210.963 102.513 210.819C102.551 210.788 102.58 210.734 102.598 210.666Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M226.495 241.464C226.574 241.146 226.404 240.838 226.228 240.981C226.19 241.013 226.161 241.066 226.143 241.135C226.061 241.453 226.234 241.761 226.41 241.618C226.448 241.586 226.477 241.533 226.495 241.464Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M281.065 296.029C281.145 295.712 280.975 295.405 280.799 295.548C280.761 295.58 280.731 295.633 280.714 295.701C280.632 296.019 280.805 296.331 280.981 296.183C281.019 296.151 281.048 296.098 281.065 296.029Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M254.369 274.063C254.448 273.744 254.278 273.437 254.102 273.58C254.064 273.612 254.034 273.665 254.017 273.734C253.937 274.052 254.108 274.36 254.284 274.216C254.322 274.184 254.352 274.131 254.369 274.063Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M373.106 175.532C373.185 175.214 373.015 174.906 372.839 175.049C372.801 175.081 372.771 175.134 372.754 175.203C372.672 175.521 372.845 175.829 373.021 175.686C373.059 175.654 373.088 175.601 373.106 175.532Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M148.943 62.7315C149.022 62.4133 148.852 62.1057 148.676 62.2488C148.638 62.2807 148.609 62.3337 148.591 62.4026C148.509 62.7209 148.682 63.0285 148.858 62.8853C148.896 62.8534 148.925 62.8004 148.943 62.7315Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M401.31 76.3623C401.389 76.0441 401.219 75.7365 401.043 75.8797C401.005 75.9115 400.976 75.9646 400.958 76.0335C400.876 76.3517 401.049 76.6593 401.225 76.5161C401.263 76.4843 401.292 76.4313 401.31 76.3623Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M407.091 17.8346C407.17 17.5172 407.001 17.2104 406.825 17.3532C406.787 17.385 406.757 17.4379 406.74 17.5066C406.658 17.824 406.831 18.1361 407.006 17.988C407.044 17.9562 407.074 17.9033 407.091 17.8346Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M380.033 146.849C380.112 146.53 379.942 146.223 379.767 146.366C379.729 146.398 379.699 146.451 379.682 146.52C379.6 146.838 379.772 147.146 379.948 147.002C379.986 146.971 380.016 146.918 380.033 146.849Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M38.0594 6.18662C38.1385 5.86926 37.9686 5.56248 37.7927 5.70529C37.7547 5.73703 37.7254 5.78993 37.7078 5.85869C37.6257 6.17605 37.7986 6.48812 37.9744 6.34002C38.0125 6.30828 38.0418 6.25538 38.0594 6.18662Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M455.677 55.3133C455.501 55.1652 455.33 55.4773 455.41 55.7946C455.427 55.8634 455.457 55.9163 455.495 55.948C455.671 56.0908 455.842 55.7841 455.762 55.4667C455.745 55.3979 455.715 55.345 455.677 55.3133Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M257.902 13.9829C257.984 13.6647 257.812 13.3571 257.636 13.5003C257.598 13.5321 257.568 13.5852 257.551 13.6541C257.472 13.9723 257.642 14.2799 257.817 14.1367C257.855 14.1049 257.885 14.0519 257.902 13.9829Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M198.095 199.264C198.175 198.946 198.004 198.638 197.828 198.782C197.79 198.813 197.76 198.866 197.743 198.935C197.663 199.254 197.834 199.561 198.01 199.418C198.048 199.386 198.078 199.333 198.095 199.264Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M221.814 22.7223C221.893 22.4049 221.723 22.0981 221.548 22.2409C221.51 22.2727 221.48 22.3256 221.463 22.3943C221.381 22.7117 221.553 23.0238 221.729 22.8757C221.767 22.8439 221.797 22.791 221.814 22.7223Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M390.594 221.621C390.676 221.303 390.503 220.995 390.327 221.138C390.289 221.17 390.26 221.223 390.242 221.292C390.163 221.611 390.333 221.918 390.509 221.775C390.547 221.743 390.576 221.69 390.594 221.621Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M56.5737 33.2241C56.6558 32.9059 56.4829 32.5983 56.3071 32.7415C56.269 32.7733 56.2397 32.8264 56.2221 32.8953C56.143 33.2135 56.3129 33.5211 56.4887 33.3779C56.5268 33.3461 56.5561 33.2931 56.5737 33.2241Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M0.372568 40.9126C0.451894 40.5944 0.281488 40.2868 0.105208 40.43C0.067014 40.4618 0.0376404 40.5149 0.0200124 40.5838C-0.0593138 40.902 0.111083 41.2096 0.287363 41.0664C0.325557 41.0346 0.35494 40.9816 0.372568 40.9126Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M58.2217 67.249C58.301 66.9308 58.1306 66.6232 57.9543 66.7664C57.9161 66.7982 57.8868 66.8513 57.8691 66.9202C57.7898 67.2384 57.9602 67.546 58.1365 67.4028C58.1747 67.371 58.2041 67.318 58.2217 67.249Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M176.891 23.1958C176.97 22.8776 176.8 22.57 176.623 22.7132C176.585 22.745 176.556 22.7981 176.538 22.867C176.459 23.1852 176.629 23.4928 176.805 23.3496C176.844 23.3178 176.873 23.2648 176.891 23.1958Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M186.609 302.81C186.691 302.492 186.519 302.184 186.343 302.327C186.305 302.359 186.275 302.412 186.258 302.481C186.179 302.799 186.349 303.107 186.524 302.964C186.562 302.932 186.592 302.879 186.609 302.81Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M438.097 293.196C438.176 292.878 438.006 292.57 437.83 292.714C437.791 292.746 437.762 292.799 437.744 292.867C437.665 293.186 437.835 293.493 438.012 293.35C438.05 293.318 438.079 293.265 438.097 293.196Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M313.915 48.3164C313.994 47.9982 313.824 47.6906 313.648 47.8338C313.61 47.8656 313.581 47.9187 313.563 47.9876C313.481 48.3058 313.654 48.6134 313.83 48.4702C313.868 48.4384 313.898 48.3854 313.915 48.3164Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M329.774 27.1841C329.856 26.8659 329.683 26.5583 329.507 26.7015C329.469 26.7333 329.44 26.7863 329.422 26.8553C329.343 27.1735 329.513 27.4811 329.689 27.3379C329.727 27.3061 329.756 27.253 329.774 27.1841Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M71.8214 164.929C71.9005 164.611 71.7305 164.303 71.5547 164.446C71.5166 164.478 71.4873 164.531 71.4697 164.6C71.3877 164.918 71.5606 165.226 71.7364 165.083C71.7745 165.051 71.8038 164.998 71.8214 164.929Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M169.43 61.3892C169.509 61.071 169.339 60.7634 169.163 60.9066C169.124 60.9384 169.095 60.9914 169.077 61.0604C168.998 61.3786 169.168 61.6862 169.345 61.543C169.383 61.5112 169.412 61.4581 169.43 61.3892Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M351.258 222.664C351.34 222.345 351.167 222.038 350.992 222.181C350.954 222.213 350.924 222.266 350.907 222.335C350.828 222.653 350.998 222.961 351.173 222.817C351.211 222.786 351.241 222.733 351.258 222.664Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M435.74 184.771C435.822 184.453 435.649 184.145 435.473 184.288C435.435 184.32 435.406 184.373 435.388 184.442C435.309 184.76 435.479 185.068 435.655 184.925C435.693 184.893 435.722 184.84 435.74 184.771Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M440.479 144.03C440.558 143.712 440.387 143.404 440.211 143.548C440.173 143.579 440.144 143.633 440.126 143.701C440.047 144.02 440.217 144.327 440.393 144.184C440.432 144.152 440.461 144.099 440.479 144.03Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M141.741 118.866C141.661 118.548 141.832 118.24 142.008 118.383C142.046 118.415 142.076 118.468 142.093 118.537C142.173 118.855 142.002 119.163 141.826 119.02C141.788 118.988 141.758 118.935 141.741 118.866Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M266.973 264.85C266.893 264.531 267.064 264.224 267.24 264.367C267.278 264.399 267.308 264.452 267.325 264.521C267.405 264.839 267.234 265.147 267.058 265.003C267.02 264.972 266.99 264.919 266.973 264.85Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M118.579 254.203C118.499 253.885 118.669 253.577 118.845 253.721C118.883 253.752 118.913 253.805 118.93 253.874C119.012 254.193 118.839 254.5 118.664 254.357C118.625 254.325 118.596 254.272 118.579 254.203Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M230.07 266.267C229.991 265.949 230.161 265.641 230.338 265.784C230.376 265.816 230.405 265.869 230.423 265.938C230.502 266.257 230.332 266.564 230.156 266.421C230.117 266.389 230.088 266.336 230.07 266.267Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M280.713 276.187C280.634 275.87 280.804 275.563 280.979 275.706C281.018 275.738 281.047 275.79 281.064 275.859C281.146 276.177 280.974 276.489 280.798 276.341C280.76 276.309 280.73 276.256 280.713 276.187Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M102.053 193.319C101.974 193.001 102.144 192.694 102.32 192.837C102.358 192.869 102.387 192.922 102.405 192.991C102.487 193.309 102.314 193.616 102.138 193.473C102.1 193.441 102.071 193.388 102.053 193.319Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M79.7978 77.9909C79.9741 77.8428 80.1445 78.1548 80.0652 78.4722C80.0476 78.541 80.0182 78.5939 79.98 78.6256C79.8037 78.7684 79.6333 78.4616 79.7126 78.1443C79.7303 78.0755 79.7596 78.0226 79.7978 77.9909Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M44.7739 184.943C44.6948 184.625 44.8647 184.317 45.0405 184.46C45.0786 184.492 45.1079 184.545 45.1255 184.614C45.2075 184.932 45.0346 185.24 44.8588 185.097C44.8207 185.065 44.7914 185.012 44.7739 184.943Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M360.912 22.6055C360.83 22.2873 361.003 21.9797 361.179 22.1229C361.217 22.1547 361.246 22.2077 361.264 22.2767C361.343 22.5949 361.173 22.9025 360.997 22.7593C360.959 22.7275 360.93 22.6744 360.912 22.6055Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M406.141 213.44C406.059 213.123 406.232 212.811 406.408 212.959C406.446 212.991 406.475 213.044 406.493 213.113C406.572 213.43 406.402 213.737 406.226 213.594C406.188 213.562 406.159 213.509 406.141 213.44Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M420.58 244.985C420.498 244.667 420.67 244.359 420.846 244.502C420.884 244.534 420.914 244.587 420.931 244.656C421.01 244.974 420.84 245.282 420.665 245.139C420.626 245.107 420.597 245.054 420.58 244.985Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M456.194 89.8106C456.115 89.4924 456.285 89.1848 456.46 89.3279C456.499 89.3598 456.528 89.4128 456.545 89.4818C456.627 89.8 456.455 90.1076 456.279 89.9644C456.241 89.9325 456.211 89.8795 456.194 89.8106Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M446.943 69.7349C446.861 69.4167 447.034 69.1091 447.21 69.2523C447.248 69.2841 447.277 69.3371 447.294 69.4061C447.374 69.7243 447.204 70.0319 447.028 69.8887C446.99 69.8569 446.96 69.8038 446.943 69.7349Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M419.686 165.637C419.604 165.319 419.777 165.011 419.953 165.155C419.991 165.186 420.02 165.239 420.038 165.308C420.117 165.627 419.947 165.934 419.771 165.791C419.733 165.759 419.704 165.706 419.686 165.637Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M299.163 0.518576C299.083 0.200366 299.253 -0.107239 299.429 0.0359558C299.467 0.0677768 299.497 0.120808 299.514 0.189754C299.596 0.507964 299.423 0.815569 299.248 0.672375C299.209 0.640553 299.18 0.587522 299.163 0.518576Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M289.885 7.94386C289.806 7.62566 289.976 7.31806 290.152 7.46125C290.191 7.49307 290.22 7.54611 290.238 7.61505C290.317 7.93326 290.146 8.24086 289.97 8.09767C289.932 8.06585 289.903 8.01281 289.885 7.94386Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M73.4182 241.463C73.3389 241.145 73.5093 240.837 73.6856 240.98C73.7238 241.012 73.7531 241.065 73.7708 241.134C73.8501 241.452 73.6797 241.76 73.5034 241.617C73.4652 241.585 73.4358 241.532 73.4182 241.463Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M25.5391 154.299C25.457 153.982 25.6299 153.669 25.8057 153.818C25.8438 153.849 25.8731 153.902 25.8907 153.971C25.9698 154.288 25.7999 154.595 25.624 154.452C25.586 154.421 25.5567 154.368 25.5391 154.299Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M39.2764 139.716C39.1943 139.398 39.3672 139.09 39.543 139.233C39.5811 139.265 39.6104 139.318 39.628 139.387C39.7071 139.705 39.5372 140.013 39.3614 139.87C39.3233 139.838 39.294 139.785 39.2764 139.716Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M108.043 155.823C107.964 155.505 108.134 155.197 108.31 155.34C108.348 155.372 108.377 155.425 108.395 155.494C108.477 155.812 108.304 156.12 108.128 155.977C108.09 155.945 108.06 155.892 108.043 155.823Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M9.04926 79.8887C8.97014 79.5705 9.14009 79.2629 9.3159 79.4061C9.35399 79.4379 9.38329 79.4909 9.40087 79.5599C9.48292 79.8781 9.31004 80.1857 9.13423 80.0425C9.09614 80.0107 9.06684 79.9576 9.04926 79.8887Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M464.018 120.96C463.939 120.642 464.109 120.334 464.285 120.477C464.323 120.509 464.352 120.562 464.37 120.631C464.452 120.949 464.279 121.257 464.103 121.114C464.065 121.082 464.036 121.029 464.018 120.96Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M481.72 96.5065C481.544 96.6497 481.374 96.3421 481.453 96.0239C481.471 95.955 481.5 95.9019 481.538 95.8701C481.714 95.7269 481.885 96.0345 481.805 96.3527C481.788 96.4217 481.758 96.4747 481.72 96.5065Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M472.164 73.6629C471.988 73.811 471.817 73.499 471.897 73.1816C471.914 73.1128 471.944 73.0599 471.982 73.0282C472.158 72.8854 472.329 73.1922 472.249 73.5095C472.232 73.5783 472.202 73.6312 472.164 73.6629Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M318.333 13.5796C318.253 13.2614 318.424 12.9538 318.6 13.097C318.638 13.1288 318.667 13.1818 318.685 13.2508C318.764 13.569 318.594 13.8766 318.418 13.7334C318.38 13.7016 318.35 13.6485 318.333 13.5796Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M82.5661 145.021C82.487 144.703 82.6569 144.395 82.8327 144.538C82.8708 144.57 82.9001 144.623 82.9177 144.692C82.9998 145.01 82.8269 145.318 82.6511 145.175C82.613 145.143 82.5837 145.09 82.5661 145.021Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M198.666 8.31399C198.587 7.99578 198.757 7.68817 198.934 7.83137C198.972 7.86319 199.001 7.91622 199.019 7.98516C199.098 8.30337 198.928 8.61098 198.751 8.46778C198.713 8.43596 198.684 8.38293 198.666 8.31399Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M177.995 49.4146C177.915 49.0964 178.085 48.7888 178.261 48.932C178.299 48.9638 178.329 49.0168 178.346 49.0857C178.428 49.404 178.255 49.7116 178.08 49.5684C178.041 49.5365 178.012 49.4835 177.995 49.4146Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M215.024 49.6157C214.944 49.2975 215.115 48.9899 215.291 49.1331C215.329 49.1649 215.359 49.218 215.376 49.2869C215.456 49.6051 215.285 49.9127 215.109 49.7695C215.071 49.7377 215.041 49.6847 215.024 49.6157Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M150.583 256.91C150.504 256.592 150.674 256.284 150.85 256.428C150.889 256.459 150.918 256.512 150.936 256.581C151.015 256.9 150.844 257.207 150.668 257.064C150.63 257.032 150.601 256.979 150.583 256.91Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M417.919 260.585C417.837 260.267 418.01 259.96 418.186 260.103C418.224 260.135 418.253 260.188 418.271 260.257C418.35 260.575 418.18 260.882 418.004 260.739C417.966 260.707 417.937 260.654 417.919 260.585Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M217.508 274.063C217.428 273.744 217.599 273.437 217.775 273.58C217.813 273.612 217.843 273.665 217.86 273.734C217.94 274.052 217.769 274.36 217.593 274.216C217.555 274.184 217.525 274.131 217.508 274.063Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M244.201 294.614C244.122 294.296 244.292 293.988 244.469 294.131C244.507 294.163 244.536 294.216 244.554 294.285C244.633 294.603 244.463 294.911 244.286 294.768C244.248 294.736 244.219 294.683 244.201 294.614Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M291.934 246.255C291.855 245.937 292.025 245.629 292.201 245.772C292.24 245.804 292.269 245.857 292.287 245.926C292.366 246.244 292.196 246.552 292.019 246.409C291.981 246.377 291.952 246.324 291.934 246.255Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M197.096 277.605C197.017 277.287 197.187 276.98 197.363 277.123C197.401 277.155 197.431 277.208 197.448 277.277C197.528 277.595 197.357 277.902 197.181 277.759C197.143 277.727 197.114 277.674 197.096 277.605Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M213.189 302.409C213.11 302.091 213.28 301.783 213.456 301.926C213.494 301.958 213.523 302.011 213.541 302.08C213.623 302.398 213.45 302.706 213.274 302.563C213.236 302.531 213.207 302.478 213.189 302.409Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M105.624 60.0464C105.545 59.7282 105.715 59.4206 105.891 59.5638C105.929 59.5956 105.958 59.6486 105.976 59.7176C106.058 60.0358 105.885 60.3434 105.709 60.2002C105.671 60.1684 105.642 60.1154 105.624 60.0464Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M336.59 250.665C336.511 250.346 336.681 250.039 336.856 250.182C336.895 250.214 336.924 250.267 336.941 250.336C337.023 250.654 336.851 250.962 336.675 250.818C336.637 250.787 336.607 250.734 336.59 250.665Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M323.068 241.632C322.986 241.314 323.159 241.007 323.335 241.15C323.373 241.182 323.402 241.235 323.42 241.304C323.499 241.622 323.329 241.929 323.153 241.786C323.115 241.754 323.086 241.701 323.068 241.632Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M391.812 113.905C391.73 113.587 391.903 113.275 392.078 113.423C392.117 113.455 392.146 113.508 392.163 113.577C392.243 113.894 392.073 114.201 391.897 114.058C391.859 114.026 391.829 113.974 391.812 113.905Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M364.974 41.2725C364.894 40.9543 365.065 40.6467 365.241 40.7899C365.279 40.8217 365.309 40.8747 365.326 40.9437C365.406 41.2619 365.235 41.5695 365.059 41.4263C365.021 41.3945 364.991 41.3414 364.974 41.2725Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M344.309 50.834C344.229 50.5158 344.399 50.2082 344.575 50.3514C344.613 50.3832 344.643 50.4362 344.66 50.5052C344.742 50.8234 344.569 51.131 344.394 50.9878C344.355 50.956 344.326 50.903 344.309 50.834Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M372.513 250.439C372.433 250.121 372.604 249.814 372.78 249.957C372.818 249.989 372.848 250.042 372.865 250.111C372.945 250.429 372.774 250.736 372.598 250.593C372.56 250.561 372.53 250.508 372.513 250.439Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M367.244 66.3238C367.162 66.0055 367.335 65.6979 367.511 65.8411C367.549 65.873 367.578 65.926 367.596 65.9949C367.675 66.3131 367.505 66.6207 367.329 66.4775C367.291 66.4457 367.262 66.3927 367.244 66.3238Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M389.455 38.7853C389.375 38.4679 389.545 38.1611 389.721 38.3039C389.759 38.3357 389.789 38.3886 389.806 38.4573C389.888 38.7747 389.715 39.0867 389.54 38.9386C389.501 38.9069 389.472 38.854 389.455 38.7853Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M407.731 172.911C407.652 172.593 407.822 172.285 407.998 172.429C408.036 172.46 408.065 172.513 408.083 172.582C408.165 172.901 407.992 173.208 407.816 173.065C407.778 173.033 407.749 172.98 407.731 172.911Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M397.605 139.078C397.526 138.759 397.697 138.452 397.873 138.595C397.911 138.627 397.94 138.68 397.958 138.749C398.037 139.067 397.867 139.375 397.691 139.231C397.652 139.2 397.623 139.147 397.605 139.078Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M442.061 220.204C441.979 219.885 442.152 219.578 442.327 219.721C442.366 219.753 442.395 219.806 442.412 219.875C442.492 220.193 442.322 220.501 442.146 220.357C442.108 220.326 442.078 220.273 442.061 220.204Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M388.67 192.566C388.591 192.248 388.761 191.941 388.937 192.084C388.975 192.116 389.004 192.169 389.022 192.238C389.104 192.556 388.931 192.863 388.755 192.72C388.717 192.688 388.688 192.635 388.67 192.566Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M454.116 198.008C454.037 197.69 454.207 197.383 454.383 197.526C454.421 197.558 454.45 197.611 454.468 197.68C454.55 197.998 454.377 198.305 454.201 198.162C454.163 198.13 454.134 198.077 454.116 198.008Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M80.831 198.063C80.7519 197.744 80.9218 197.437 81.0976 197.58C81.1357 197.612 81.165 197.665 81.1826 197.734C81.2647 198.052 81.0918 198.36 80.916 198.216C80.8779 198.184 80.8486 198.131 80.831 198.063Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M392.269 253.541C392.189 253.222 392.36 252.915 392.536 253.058C392.574 253.09 392.603 253.143 392.621 253.212C392.7 253.53 392.53 253.838 392.354 253.694C392.316 253.663 392.286 253.609 392.269 253.541Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M298.378 274.063C298.299 273.744 298.469 273.437 298.646 273.58C298.684 273.612 298.713 273.665 298.731 273.734C298.81 274.052 298.64 274.36 298.463 274.216C298.425 274.184 298.396 274.131 298.378 274.063Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M386.257 287.653C386.175 287.335 386.348 287.027 386.524 287.17C386.562 287.202 386.591 287.255 386.609 287.324C386.688 287.642 386.518 287.95 386.342 287.807C386.304 287.775 386.275 287.722 386.257 287.653Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M335.296 73.5108C335.378 73.1926 335.205 72.8849 335.029 73.0281C334.991 73.06 334.962 73.113 334.945 73.1819C334.865 73.5002 335.035 73.8078 335.211 73.6646C335.249 73.6327 335.279 73.5797 335.296 73.5108Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M382.799 145.086C382.879 144.768 382.708 144.46 382.532 144.603C382.494 144.635 382.464 144.688 382.447 144.757C382.367 145.075 382.538 145.383 382.714 145.24C382.752 145.208 382.782 145.155 382.799 145.086Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M365.285 96.8745C365.203 96.5563 365.376 96.2487 365.552 96.3919C365.59 96.4237 365.619 96.4768 365.637 96.5457C365.716 96.8639 365.546 97.1715 365.37 97.0283C365.332 96.9965 365.303 96.9435 365.285 96.8745Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M362.625 112.475C362.542 112.157 362.715 111.849 362.891 111.992C362.929 112.024 362.959 112.077 362.976 112.146C363.055 112.465 362.885 112.772 362.709 112.629C362.671 112.597 362.642 112.544 362.625 112.475Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M386.766 72.0933C386.684 71.7751 386.857 71.4675 387.033 71.6107C387.071 71.6425 387.1 71.6955 387.118 71.7645C387.197 72.0827 387.027 72.3903 386.851 72.2471C386.813 72.2153 386.784 72.1622 386.766 72.0933Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M336.974 105.43C336.895 105.111 337.065 104.804 337.241 104.947C337.28 104.979 337.309 105.032 337.327 105.101C337.406 105.419 337.236 105.727 337.059 105.584C337.021 105.552 336.992 105.499 336.974 105.43Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M330.963 139.542C330.881 139.224 331.054 138.917 331.23 139.06C331.268 139.092 331.297 139.145 331.315 139.214C331.394 139.532 331.224 139.839 331.048 139.696C331.01 139.664 330.98 139.611 330.963 139.542Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M364.401 206.748C364.319 206.429 364.492 206.122 364.668 206.265C364.706 206.297 364.735 206.35 364.753 206.419C364.832 206.737 364.662 207.045 364.486 206.901C364.448 206.87 364.419 206.817 364.401 206.748Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M465.151 166.528C465.072 166.21 465.242 165.903 465.418 166.046C465.456 166.078 465.485 166.131 465.503 166.2C465.585 166.518 465.412 166.825 465.236 166.682C465.198 166.65 465.169 166.597 465.151 166.528Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M496.895 54.5291C496.719 54.6772 496.548 54.3652 496.627 54.0478C496.645 53.9791 496.674 53.9262 496.713 53.8944C496.889 53.7516 497.059 54.0584 496.98 54.3758C496.962 54.4445 496.933 54.4974 496.895 54.5291Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M304.743 99.7315C304.822 99.4133 304.652 99.1057 304.476 99.2488C304.438 99.2807 304.408 99.3337 304.391 99.4026C304.311 99.7209 304.482 100.028 304.658 99.8853C304.696 99.8534 304.726 99.8004 304.743 99.7315Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M296.94 193.181C297.022 192.863 296.849 192.555 296.674 192.699C296.635 192.73 296.606 192.783 296.589 192.852C296.509 193.171 296.679 193.478 296.855 193.335C296.893 193.303 296.923 193.25 296.94 193.181Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M279.929 106.272C280.008 105.954 279.838 105.647 279.663 105.79C279.625 105.822 279.595 105.875 279.578 105.944C279.496 106.262 279.668 106.569 279.844 106.426C279.882 106.394 279.912 106.341 279.929 106.272Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M309.456 127.369C309.535 127.051 309.365 126.743 309.188 126.887C309.15 126.918 309.121 126.971 309.103 127.04C309.024 127.359 309.194 127.666 309.37 127.523C309.409 127.491 309.438 127.438 309.456 127.369Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M211.578 169.18C211.66 168.862 211.488 168.554 211.312 168.698C211.274 168.729 211.244 168.782 211.227 168.851C211.148 169.17 211.318 169.477 211.493 169.334C211.531 169.302 211.561 169.249 211.578 169.18Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M278.046 220.204C278.128 219.885 277.955 219.578 277.779 219.721C277.741 219.753 277.712 219.806 277.694 219.875C277.615 220.193 277.785 220.501 277.961 220.357C277.999 220.326 278.028 220.273 278.046 220.204Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M340.074 232.251C340.153 231.933 339.983 231.626 339.807 231.769C339.769 231.801 339.74 231.854 339.722 231.923C339.64 232.241 339.813 232.548 339.989 232.405C340.027 232.373 340.056 232.32 340.074 232.251Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M193.252 101.149C193.334 100.831 193.161 100.523 192.986 100.666C192.947 100.698 192.918 100.751 192.901 100.82C192.821 101.138 192.991 101.446 193.167 101.303C193.205 101.271 193.235 101.218 193.252 101.149Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M191.286 196.126C191.365 195.808 191.195 195.501 191.019 195.644C190.981 195.676 190.952 195.729 190.934 195.798C190.852 196.116 191.025 196.423 191.201 196.28C191.239 196.248 191.268 196.195 191.286 196.126Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M262.747 112.796C262.826 112.478 262.656 112.171 262.48 112.314C262.442 112.346 262.413 112.399 262.396 112.468C262.313 112.785 262.486 113.097 262.662 112.949C262.7 112.917 262.73 112.864 262.747 112.796Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M235.689 241.809C235.768 241.491 235.598 241.183 235.422 241.326C235.384 241.358 235.355 241.411 235.337 241.48C235.255 241.798 235.428 242.106 235.604 241.963C235.642 241.931 235.671 241.878 235.689 241.809Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M311.333 150.274C311.156 150.126 310.986 150.438 311.065 150.755C311.083 150.824 311.112 150.877 311.151 150.908C311.327 151.051 311.497 150.745 311.418 150.427C311.4 150.358 311.371 150.305 311.333 150.274Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M169.574 143.277C169.653 142.959 169.483 142.652 169.308 142.795C169.27 142.827 169.24 142.88 169.223 142.949C169.141 143.267 169.313 143.574 169.489 143.431C169.527 143.399 169.557 143.346 169.574 143.277Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M185.431 122.145C185.513 121.827 185.34 121.519 185.165 121.662C185.126 121.694 185.097 121.747 185.08 121.816C185 122.134 185.17 122.442 185.346 122.299C185.384 122.267 185.414 122.214 185.431 122.145Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M296.134 238.992C296.214 238.674 296.043 238.366 295.867 238.509C295.829 238.541 295.799 238.594 295.782 238.663C295.702 238.981 295.873 239.289 296.049 239.146C296.087 239.114 296.117 239.061 296.134 238.992Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M216.568 117.566C216.486 117.248 216.658 116.941 216.834 117.084C216.872 117.116 216.902 117.169 216.919 117.238C216.998 117.556 216.828 117.863 216.653 117.72C216.615 117.688 216.585 117.635 216.568 117.566Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M311.85 184.771C311.77 184.453 311.94 184.145 312.116 184.288C312.154 184.32 312.184 184.373 312.201 184.442C312.283 184.76 312.11 185.068 311.935 184.925C311.896 184.893 311.867 184.84 311.85 184.771Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M302.599 164.696C302.517 164.378 302.689 164.07 302.865 164.213C302.903 164.245 302.933 164.298 302.95 164.367C303.029 164.685 302.859 164.993 302.684 164.85C302.646 164.818 302.616 164.765 302.599 164.696Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M319.674 215.921C319.595 215.603 319.765 215.295 319.94 215.438C319.979 215.47 320.008 215.523 320.025 215.592C320.107 215.91 319.935 216.218 319.759 216.075C319.721 216.043 319.691 215.99 319.674 215.921Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M337.376 191.467C337.2 191.611 337.029 191.303 337.109 190.985C337.126 190.916 337.156 190.863 337.194 190.831C337.37 190.688 337.541 190.995 337.461 191.314C337.444 191.383 337.414 191.436 337.376 191.467Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M327.82 168.624C327.644 168.772 327.473 168.46 327.553 168.143C327.57 168.074 327.6 168.021 327.638 167.989C327.814 167.846 327.985 168.153 327.905 168.47C327.888 168.539 327.858 168.592 327.82 168.624Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M173.988 108.54C173.909 108.222 174.079 107.914 174.256 108.057C174.294 108.089 174.323 108.142 174.341 108.211C174.42 108.529 174.25 108.837 174.073 108.694C174.035 108.662 174.006 108.609 173.988 108.54Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M247.467 208.866C247.385 208.549 247.558 208.237 247.734 208.385C247.772 208.417 247.801 208.47 247.819 208.538C247.898 208.856 247.728 209.162 247.552 209.02C247.514 208.988 247.485 208.935 247.467 208.866Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M220.629 136.233C220.55 135.915 220.72 135.608 220.897 135.751C220.935 135.783 220.964 135.836 220.982 135.905C221.061 136.223 220.891 136.53 220.715 136.387C220.676 136.355 220.647 136.302 220.629 136.233Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M199.967 145.794C199.888 145.476 200.058 145.169 200.234 145.312C200.272 145.344 200.302 145.397 200.319 145.466C200.401 145.784 200.228 146.091 200.052 145.948C200.014 145.916 199.985 145.863 199.967 145.794Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M222.9 161.285C222.818 160.967 222.991 160.659 223.167 160.803C223.205 160.834 223.234 160.887 223.252 160.956C223.331 161.275 223.161 161.582 222.985 161.439C222.947 161.407 222.918 161.354 222.9 161.285Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M179.429 158.549C179.35 158.232 179.52 157.925 179.696 158.068C179.734 158.1 179.763 158.153 179.781 158.221C179.863 158.539 179.69 158.851 179.514 158.703C179.476 158.671 179.447 158.618 179.429 158.549Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M253.261 234.039C253.182 233.72 253.352 233.413 253.528 233.556C253.567 233.588 253.596 233.641 253.614 233.71C253.693 234.028 253.522 234.336 253.346 234.192C253.308 234.161 253.279 234.108 253.261 234.039Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M352.554 149.49C352.378 149.638 352.207 149.326 352.287 149.008C352.304 148.94 352.334 148.887 352.372 148.855C352.548 148.712 352.718 149.019 352.639 149.336C352.622 149.405 352.592 149.458 352.554 149.49Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M89.5288 173.433C89.4468 173.114 89.6197 172.807 89.7955 172.95C89.8336 172.982 89.8629 173.035 89.8804 173.104C89.9595 173.422 89.7896 173.73 89.6138 173.586C89.5757 173.555 89.5464 173.502 89.5288 173.433Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M170.007 276.897C169.925 276.579 170.098 276.271 170.273 276.414C170.312 276.446 170.341 276.499 170.358 276.568C170.438 276.886 170.268 277.194 170.092 277.051C170.054 277.019 170.024 276.966 170.007 276.897Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M131.928 273.354C131.846 273.035 132.019 272.728 132.194 272.871C132.232 272.903 132.262 272.956 132.279 273.025C132.358 273.343 132.189 273.651 132.013 273.507C131.975 273.476 131.945 273.422 131.928 273.354Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M168.437 250.676C168.354 250.358 168.527 250.05 168.703 250.194C168.741 250.225 168.771 250.279 168.788 250.347C168.867 250.666 168.697 250.973 168.522 250.83C168.483 250.798 168.454 250.745 168.437 250.676Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M122.569 194.09C122.487 193.772 122.659 193.465 122.835 193.608C122.873 193.64 122.903 193.693 122.92 193.762C122.999 194.08 122.829 194.387 122.654 194.244C122.615 194.212 122.586 194.159 122.569 194.09Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M167.995 192.615C167.913 192.297 168.085 191.989 168.261 192.132C168.299 192.164 168.329 192.217 168.346 192.286C168.425 192.604 168.255 192.912 168.08 192.769C168.042 192.737 168.012 192.684 167.995 192.615Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M141.557 235.365C141.477 235.047 141.648 234.739 141.824 234.883C141.862 234.914 141.892 234.967 141.909 235.036C141.989 235.355 141.818 235.662 141.642 235.519C141.604 235.487 141.574 235.434 141.557 235.365Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M283.813 30.2822C283.892 29.964 283.722 29.6564 283.546 29.7996C283.508 29.8314 283.479 29.8845 283.461 29.9534C283.379 30.2716 283.552 30.5792 283.728 30.436C283.766 30.4042 283.795 30.3512 283.813 30.2822Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M272.034 93.353C272.114 93.0348 271.944 92.7272 271.768 92.8704C271.73 92.9023 271.7 92.9553 271.683 93.0242C271.601 93.3424 271.774 93.65 271.95 93.5068C271.988 93.475 272.017 93.422 272.034 93.353Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M234.779 60.0298C234.7 59.7116 234.87 59.404 235.046 59.5472C235.084 59.579 235.113 59.632 235.131 59.701C235.213 60.0192 235.04 60.3268 234.864 60.1836C234.826 60.1518 234.797 60.0987 234.779 60.0298Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M266.784 62.7368C266.704 62.4186 266.875 62.111 267.051 62.2542C267.089 62.286 267.119 62.3391 267.136 62.408C267.216 62.7262 267.045 63.0338 266.869 62.8906C266.831 62.8588 266.801 62.8058 266.784 62.7368Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M286.208 82.7237C286.125 82.4054 286.298 82.0978 286.474 82.241C286.512 82.2729 286.542 82.3259 286.559 82.3948C286.638 82.713 286.468 83.0206 286.293 82.8775C286.254 82.8456 286.225 82.7926 286.208 82.7237Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M248.13 79.1802C248.048 78.862 248.221 78.5544 248.397 78.6976C248.435 78.7294 248.464 78.7824 248.482 78.8514C248.561 79.1696 248.391 79.4772 248.215 79.334C248.177 79.3022 248.148 79.2491 248.13 79.1802Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M284.639 56.5034C284.557 56.1852 284.73 55.8776 284.906 56.0208C284.944 56.0526 284.973 56.1057 284.991 56.1746C285.07 56.4928 284.9 56.8004 284.724 56.6572C284.686 56.6254 284.657 56.5724 284.639 56.5034Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M257.759 41.1919C257.68 40.8737 257.85 40.5661 258.026 40.7093C258.065 40.7411 258.094 40.7942 258.112 40.8631C258.191 41.1813 258.021 41.4889 257.844 41.3457C257.806 41.3139 257.777 41.2609 257.759 41.1919Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M78.2461 222.708C78.1668 222.39 78.3372 222.082 78.5134 222.225C78.5516 222.257 78.581 222.31 78.5986 222.379C78.678 222.697 78.5076 223.005 78.3313 222.862C78.2931 222.83 78.2637 222.777 78.2461 222.708Z"
            fill="white"
            fill-opacity="0.8"
          />
          <path
            d="M32.604 55.0855C32.5247 54.7673 32.6951 54.4597 32.8713 54.6029C32.9095 54.6347 32.9389 54.6877 32.9566 54.7566C33.0359 55.0749 32.8655 55.3825 32.6892 55.2393C32.651 55.2074 32.6216 55.1544 32.604 55.0855Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M127.917 19.6393C127.838 19.3219 128.008 19.0151 128.184 19.1579C128.222 19.1897 128.251 19.2426 128.269 19.3113C128.351 19.6287 128.178 19.9408 128.002 19.7927C127.964 19.7609 127.935 19.708 127.917 19.6393Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M85.771 6.49171C85.6917 6.1735 85.8621 5.86591 86.0383 6.0091C86.0765 6.04092 86.1059 6.09395 86.1235 6.1629C86.2029 6.48111 86.0325 6.78871 85.8562 6.64552C85.818 6.6137 85.7886 6.56066 85.771 6.49171Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M85.6021 34.5342C85.52 34.216 85.6929 33.9084 85.8687 34.0516C85.9068 34.0834 85.9361 34.1364 85.9537 34.2054C86.0328 34.5236 85.8628 34.8312 85.687 34.688C85.6489 34.6562 85.6196 34.6031 85.6021 34.5342Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M150.771 39.4951C150.689 39.1769 150.862 38.8693 151.038 39.0125C151.076 39.0443 151.105 39.0974 151.123 39.1663C151.202 39.4845 151.032 39.7921 150.856 39.6489C150.818 39.6171 150.789 39.5641 150.771 39.4951Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M49.8769 102.566C49.7976 102.248 49.968 101.94 50.1443 102.083C50.1825 102.115 50.2119 102.168 50.2295 102.237C50.3088 102.555 50.1384 102.863 49.9621 102.72C49.924 102.688 49.8946 102.635 49.8769 102.566Z"
            fill="white"
            fill-opacity="0.5"
          />
          <path
            d="M29.3527 93.6641C29.2736 93.3459 29.4436 93.0383 29.6194 93.1815C29.6575 93.2133 29.6868 93.2663 29.7043 93.3353C29.7864 93.6535 29.6135 93.9611 29.4377 93.8179C29.3996 93.7861 29.3703 93.733 29.3527 93.6641Z"
            fill="white"
            fill-opacity="0.5"
          />
        </svg>
        <h1 class="md:text-3xl lg:text-2xl text-xl font-bold">MCQ Test</h1>
        <p class="sm:text-lg xl:text-sm font-sdfd">
          Please complete the MCQ test as part of the enrollment process. Based
          on your ranking, candidates will be selected for the course.
        </p>
        <a
          href="/kodex/mcq?userId=<%= myRankings?.userId %>"
          class="button mt-5"
        >
          <button
            class="bg-[#525252] px-14 py-1.5 relative z-[22] rounded-full text-white"
          >
            Start
          </button>
        </a>
      </div>
    </div>

    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.5.0/axios.min.js"
      integrity="sha512-aoTNnqZcT8B4AmeCFmiSnDlc4Nj/KPaZyB5G7JnOnUEkdNpCZs1LCankiYi01sLTyWy+m2P+W4XM+BuQ3Q4/Dg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js"
      integrity="sha512-NcZdtrT77bJr4STcmsGAESr06BYGE8woZdSdEgqnpyqac7sugNO+Tr4bGwGF3MsnEkGKhU2KL2xh6Ec+BqsaHA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>

    <script>

      if(<%= myRankings?.given ? 'true' : 'false' %> === 'true') {
        document.querySelector("#rank-improvement-popup").style.display = "none";
      }

      const data = localStorage.getItem("codexRegistered");
      const urlParams = new URLSearchParams(window.location.search);
      const userId = urlParams.get("id");
      if (!userId) {
        console.log("User ID not found in params");
        if (!data) {
          document.querySelector("#rank-improvement-popup").style.display =
            "none";
          // window.location.href = "/kodex/registration";
        } else {
          window.location.href = "/kodex/ranking?id=" + JSON.parse(data);
        }
      }

      gsap.from("#rank-improvement-popup", {
        duration: 1,
        y: -400,
        opacity: 0,
        ease: "power2.out",
      });
      // Handle skip button click
      document.getElementById("skip-btn").addEventListener("click", () => {
        gsap.to("#rank-improvement-popup", {
          duration: 1,
          y: 400,
          opacity: 0,
          ease: "power2.out",
        });
      });

      // Handle start button click - Add your logic here
      document.querySelectorAll(".start-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          document.querySelector(".ranks").style.display = "none";
          if (!userId) return (window.location.href = "/kodex/registration");
          document.querySelector(".navigate-popup").style.display = "flex";
        });
      });
    </script>
  </body>
</html>

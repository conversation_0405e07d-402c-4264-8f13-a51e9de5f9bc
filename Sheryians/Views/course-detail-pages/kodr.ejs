<%- include('../new-partials/header.ejs') %>
    <link rel="stylesheet" href="/css/common.css">
    <link rel="stylesheet" href="/css/course-details-css/kodr.css">

    <script defer>
        function hidePreLoader() {
            document.querySelector('#preLoder').style.opacity = '0'
            setTimeout(() => {
                document.querySelector('#preLoder').style.display = 'none'
            }, 500);
        }

        function observeAndCallback(element, callback, options) {
            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        callback(entry.target); // Run the callback when the element is intersecting
                        observer.disconnect(); // Disconnect the observer after the callback is executed
                    }
                });
            }, options);

            observer.observe(element);
        }

        function applyLazyLoading() {
            document.querySelectorAll('[load-lazy]').forEach(lazyLoadingElement => {

                function callbackFunction() {
                    const src = lazyLoadingElement.getAttribute('load-lazy')
                    lazyLoadingElement.removeAttribute('load-lazy')
                    lazyLoadingElement.setAttribute('src', src)
                }

                observeAndCallback(lazyLoadingElement, callbackFunction, {
                    threshold: 0.5
                });
            })
        }

        window.addEventListener('DOMContentLoaded', (event) => {

            applyLazyLoading()
        })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/css/common.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@18.1.1/build/css/intlTelInput.css">

    <script defer="">
        function hidePreLoader() {
            document.querySelector('#preLoder').style.opacity = '0'
            setTimeout(() => {
                document.querySelector('#preLoder').style.display = 'none'
            }, 500);
        }

        function observeAndCallback(element, callback, options) {
            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        callback(entry.target); // Run the callback when the element is intersecting
                        observer.disconnect(); // Disconnect the observer after the callback is executed
                    }
                });
            }, options);

            observer.observe(element);
        }

        function applyLazyLoading() {
            document.querySelectorAll('[load-lazy]').forEach(lazyLoadingElement => {

                function callbackFunction() {
                    const src = lazyLoadingElement.getAttribute('load-lazy')
                    lazyLoadingElement.removeAttribute('load-lazy')
                    lazyLoadingElement.setAttribute('src', src)
                }

                observeAndCallback(lazyLoadingElement, callbackFunction, {
                    threshold: 0.5
                });
            })
        }

        window.addEventListener('DOMContentLoaded', (event) => {

            applyLazyLoading()
        })
    </script>
    <title>Koder 3.0</title>
    <script>
        !function (f, b, e, v, n, t, s) {
            if (f.fbq) return; n = f.fbq = function () {
                n.callMethod ?
                    n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };
            if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
            n.queue = []; t = b.createElement(e); t.async = !0;
            t.src = v; s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s)
        }(window, document, 'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '632198189232294');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id=632198189232294&ev=PageView&noscript=1" /></noscript>
    <!-- End Meta Pixel Code -->
    </head>

    <body>

        <div id="main">
            <section id="page1" class="page">
                <div class="left">
                    <div class="title">
                        <h1>100 Days In-House Bootcamp Programme - Kodr.</h1>
                    </div>


                    <div style="display: flex; gap: 2rem; flex-wrap: wrap;">
                        <div class="activeSupport">
                            Starts on: 3 November 2025 <img
                                src="https://ik.imagekit.io/sheryians/Aptitude%20&amp;%20Reasoning/360_F_621906809_C9rwm43Z7HQzYnrbTfMhmeyznilVgRFk%201_84ajxxlmg.png"
                                alt="">
                        </div>
                    </div>

                    <!-- <div class="activeSupport">
                    Course Supports Active Updates <img
                        src="https://ik.imagekit.io/sheryians/Aptitude%20&amp;%20Reasoning/360_F_621906809_C9rwm43Z7HQzYnrbTfMhmeyznilVgRFk%201_84ajxxlmg.png"
                        alt=""
                    >
                </div> -->

                    <div class="price">
                        <p>
                            Fee
                            <span class="priceValue">
                                <span>₹</span>
                                82599
                            </span>
                            <span class="discount">[ 69999 + 18% GST ]</span>

                        </p>
                    </div>
                    <div class="buy">


                        <br>

                        <p class="buyNow" onclick='document.querySelector(".form-container").scrollIntoView()'>Register
                            Now</p>


                    </div>




                </div>
                <div class="right">
                    <div id="playOverlay">
                        <!-- <i id="playBtn" class="ri-play-circle-line"></i> -->
                        <img src="https://ik.imagekit.io/sheryians/kodr/kodr-3.0%20Large_6OYmH4-6N.jpeg" alt="">
                    </div>
                    <!-- <div id="embedded-promo">
                        <iframe id="promo-video" width="100%" height="100%"
                            src="https://www.youtube.com/embed/AptmHn3bJoY?si=jncXEEt0lgXQ8Oxi"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen=""></iframe>
                    </div> -->
                    <!-- <div class="tags">
                    <div class="tag">
                        Bootcamp
                    </div>
                </div> -->
                </div>
            </section>

            <section class="page outline">
                <div class="text">
                    <h1>We Believe in,</h1>
                    <h1><span class="key-highlight">Consistency.</span> <br> <span class="margin">No Shortcuts.</span>
                    </h1>
                </div>
            </section>

            <section class="page non-stop">
                <h1>Non-Stop</h1>
                <h1 class="key-light">4 Hours</h1>
                <h1>Coding <span>[ Everyday ]</span></h1>
                <p>
                    <span>Monday to Saturday.</span>
                    <span>10:00 AM to 2:00 PM.</span>
                </p>
            </section>

            <section class="page page4">

                <div class="text">
                    <h1 class="heading">Dominate.</h1>
                    <h2 class="subTitle">From Start to Victory.</h2>
                </div>


            </section>

            <section class="page page5 syllabus">


                <div class="accordian ">
                    <div class="top">
                        <h1>Phase 1: Full MERN Stack Mastery</h1>
                        <button> <span>Expand</span>
                            <div class="icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg></div>
                        </button>
                    </div>
                    <div class="panel">
                        <div class="panelContent">
                            <p>MongoDB Schema Design</p>
                            <p>Mongoose Validation</p>
                            <p>Database Indexing</p>
                            <p>Aggregation Pipelines</p>
                            <p>Redis Caching</p>
                            <p>DAO Pattern</p>
                            <p>RESTful API Design</p>
                            <p>Express Middleware</p>
                            <p>JWT Authentication</p>
                            <p>Password Hashing (bcryptjs)</p>
                            <p>Rate Limiting</p>
                            <p>CORS Configuration</p>
                            <p>Security Best Practices (Helmet)</p>
                            <p>Performance Tuning</p>
                            <p>React Component Architecture</p>
                            <p>React Hooks</p>
                            <p>Context API</p>
                            <p>Form Handling & Validation</p>
                            <p>State Management Patterns</p>
                            <p>Responsive Design</p>
                            <p>Code Splitting</p>
                            <p>ESLint & Prettier</p>
                            <p>Jest Testing</p>
                            <p>Git Hooks (Husky)</p>
                            <p>Postman API Testing</p>
                        </div>
                    </div>
                </div>

                <div class="accordian ">
                    <div class="top">
                        <h1>Phase 2: AI Integration</h1>
                        <button> <span>Expand</span>
                            <div class="icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg></div>
                        </button>
                    </div>
                    <div class="panel">
                        <div class="panelContent">
                            <p>Gemini API</p>
                            <p>Prompt Engineering</p>
                            <p>AI Error Handling</p>
                            <p>LangChain Basics</p>
                            <p>Memory Management</p>
                            <p>RAG (Retrieval-Augmented Generation)</p>
                            <p>Vector Databases</p>
                            <p>MCP Server</p>
                            <p>AI Chatbot/Agent Development</p>
                        </div>
                    </div>
                </div>

                <div class="accordian ">
                    <div class="top">
                        <h1>Phase 3: Deployment & DevOps</h1>
                        <button> <span>Expand</span>
                            <div class="icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg></div>
                        </button>
                    </div>
                    <div class="panel">
                        <div class="panelContent">
                            <p>Vercel Deployment</p>
                            <p>Render Deployment</p>
                            <p>AWS EC2 Setup</p>
                            <p>Docker Containerization</p>
                            <p>Application Monitoring</p>
                            <p>GitHub Actions</p>
                            <p>CI/CD Pipelines</p>
                            <p>Environment Management</p>
                            <p>Secrets Management</p>
                            <p>Environment Variables</p>
                        </div>
                    </div>
                </div>

                <div class="accordian ">
                    <div class="top">
                        <h1>Phase 4: Debugging & Self-Reliance</h1>
                        <button> <span>Expand</span>
                            <div class="icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg></div>
                        </button>
                    </div>
                    <div class="panel">
                        <div class="panelContent">
                            <p>Browser Log Analysis</p>
                            <p>Node.js Logs</p>
                            <p>Deployment Logs</p>
                            <p>Browser Developer Tools</p>
                            <p>Network Debugging</p>
                            <p>Postman Debugging</p>
                            <p>Error Handling</p>
                            <p>Performance Profiling</p>
                            <p>Memory Leak Detection</p>
                        </div>
                    </div>
                </div>

                <div class="accordian ">
                    <div class="top">
                        <h1>Phase 5: Project</h1>
                        <button> <span>Expand</span>
                            <div class="icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg></div>
                        </button>
                    </div>
                    <div class="panel">
                        <div class="panelContent">
                            <p>Production-Ready MERN App</p>
                            <p>User Authentication</p>
                            <p>CRUD Operations</p>
                            <p>Data Validation</p>
                            <p>AI-Powered Features</p>
                            <p>Platform Deployment</p>
                            <p>API Documentation</p>
                            <p>GitHub Repository</p>
                            <p>CI/CD Implementation</p>
                            <p>Coding Standards</p>
                            <p>Redis Caching</p>
                            <p>Scalable Architecture</p>
                        </div>
                    </div>
                </div>
            </section>


            <!-- <section class="page page3">
            <div class="text">

                <h1>Training</h1>
                <h1>which <span><span>matters.</span> </span> </h1>
<h2 class="subHeading">Beyond Projects, Towards Purpose.</h2> 
            </div>
        </section> -->

            <section class="page form">
                <%if(seatsLeft> 0){%>
                    <div class="form-container">
                        <h2>Register now <span>👇</span></h2>
                        <p>To join batch & payment fill this form</p>
                        <form>
                            <div class="input-group">
                                <label for="full-name">Full Name</label>
                                <input type="text" id="full-name" placeholder="Enter Your Name Here">
                                <span class="error-message"></span> <!-- Error message span -->
                            </div>
                            <div class="input-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" placeholder="Enter Your Email Here">
                                <span class="error-message"></span> <!-- Error message span -->
                            </div>
                            <div class="input-group">
                                <label for="contact">Contact Number</label>
                                <input type="text" id="contact" placeholder="Enter Your Number Here">
                                <span class="error-message"></span> <!-- Error message span -->
                            </div>

                            <div class="input-group">
                                <label for="education">Educational Background</label>
                                <input type="text" id="education" placeholder="Enter Your Educational Background">
                                <span class="error-message"></span> <!-- Error message span -->
                            </div>
                            <div class="input-group">
                                <label for="experience">Programming Experience</label>
                                <select id="experience">
                                    <option value="beginner">Beginner</option>
                                    <option value="intermediate">Intermediate</option>
                                    <option value="advanced">Advanced</option>
                                </select>
                                <span class="error-message"></span>
                            </div>
                            <div class="input-group">
                                <label for="dob">Date of Birth</label>
                                <input type="date" id="dob">
                                <span class="error-message"></span> <!-- Error message span -->
                            </div>

                            <div class="input-group">
                                <label for="tnc">I agree to the Terms and Conditions <a target="_blank"
                                        href="https://sheryians.notion.site/terms-and-conditions-kodr-bootcamp">read
                                        here</a> </label>
                                <input type="checkbox" required id="tnc">
                            </div>

                            <button class="submitBtn" type="submit">Submit</button>
                        </form>
                    </div>
                    <%}%>


                        <script>
                            // const inputGroup = document.querySelector('.input-group');
                            // const dobInput = document.querySelector('#dob');

                            // // Add a click event to the input-group
                            // inputGroup.addEventListener('click', () => {
                            //     dobInput.focus();  // Focus on the input field when clicking anywhere in the input-group
                            // });
                            document.querySelector('form').addEventListener('submit', async function (event) {
                                event.preventDefault();

                                let isValid = true;
                                const name = document.getElementById('full-name').value.trim();
                                const email = document.getElementById('email').value.trim();
                                const contact = document.getElementById('contact').value.trim();
                                const education = document.getElementById('education').value.trim();
                                const experience = document.getElementById('experience').value;
                                const dob = document.getElementById('dob').value;

                                // Clear previous error messages
                                document.querySelectorAll('.error-message').forEach(span => span.textContent = '');

                                if (name === '') {
                                    showError('full-name', 'Full Name is required');
                                    isValid = false;
                                }

                                if (email === '' || !/^\S+@\S+\.\S+$/.test(email)) {
                                    showError('email', 'Valid Email Address is required');
                                    isValid = false;
                                }

                                if (contact === '' || !/^\d{10}$/.test(contact)) {
                                    showError('contact', 'Valid Contact Number is required');
                                    isValid = false;
                                }

                                if (education === '') {
                                    showError('education', 'Educational Background is required');
                                    isValid = false;
                                }

                                if (experience === '') {
                                    showError('experience', 'Programming Experience is required');
                                    isValid = false;
                                }

                                if (dob === '') {
                                    showError('dob', 'Date of Birth is required');
                                    isValid = false;
                                }

                                if (isValid) {
                                    try {
                                        document.querySelector(".submitBtn").innerHTML = "<div class='loader'></div>"
                                        const { data } = await axios.post("/kodr/register", { name, email, contact, education, experience, dob })
                                        document.querySelector(".confirmPopup").classList.add("show")
                                        document.querySelector(".submitBtn").innerHTML = "Submit"
                                        disableForm()
                                    } catch (err) {
                                        document.querySelector(".submitBtn").innerHTML = "Submit"
                                        if (err.response.data.message == "Email already registered") {
                                            showError('email', 'Email already registered');
                                        }
                                        if (err.response.data.message == "Phone already registered") {
                                            showError('contact', 'Phone number already registered');
                                        }
                                        console.log(err)
                                    }
                                }

                            });
                            function disableForm() {
                                const formElements = document.querySelectorAll('form input, form select, form textarea, form button');
                                formElements.forEach(element => {
                                    element.disabled = true;  // Disable each form element
                                });
                            }
                            function showError(inputId, message) {
                                const inputGroup = document.getElementById(inputId).closest('.input-group');
                                const errorSpan = inputGroup.querySelector('.error-message');
                                errorSpan.textContent = message;
                            }
                        </script>


                        <!-- <div class="payment-details">





                <img
                    src="payment.png"
                    alt=""
                >

                <p>
                    <span>OR</span> <br> <br>
                    Pay via UPI: <span>sheryians@ybl</span>
                </p>



                <p>
                    Pay ₹ 59000 using this QR code or UPI Id and send the screenshot to
                    <span style="text-wrap: nowrap;">
                        +91 9993478545 [ Whatsapp ]
                    </span>
                    to confirm your registration. <br>
                    Or contact the same for any queries.
                </p>
            </div> -->

            </section>
        </div>
        <div class="confirmPopup" id="confirmPopup">
            <div class="popup-body">
                <i class="ri-checkbox-circle-line"></i>
                <p>You have successfully registered! Our team will contact you soon.</p>
                <button
                    onclick='this.closest(".confirmPopup").classList.remove("show"); location.reload();'>Okay</button>
            </div>
        </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@18.1.1/build/js/intlTelInput.min.js"></script>
        <script defer="" src="/js/singleCourse.js"></script>
        <!-- <script src="index.js"></script> -->

        <script>
            feather.replace();

            document.querySelectorAll('.accordian .top').forEach(accordian => {
                accordian.addEventListener('click', (event) => {
                    event.target.closest('.accordian').classList.toggle('active')
                })
            })

            const mouse = {
                x: 0,
                y: 0
            }

            addEventListener('mousemove', (event) => {
                mouse.x = event.clientX
                mouse.y = event.clientY
            })

            const images = document.querySelectorAll('.floatingImage')


            function moveFloatingImages() {
                requestAnimationFrame(moveFloatingImages)

                images.forEach(image => {
                    image.style.top = mouse.y + 'px'
                    image.style.left = mouse.x + 'px'
                })



            }
            requestAnimationFrame(moveFloatingImages)

            function enroll() {
                document.querySelector(".form-container").scrollIntoView()
                // window.scrollTo({
                //     bottom: 400,
                //     behavior: 'smooth'
                // });
            }

        </script>
        <style>
            /* Container styling */
            .callToActionHover {
                opacity: 0;
                position: fixed;
                bottom: 2%;
                transform: translateX(-50%);
                left: 50%;
                display: flex;
                gap: 2rem;
                /* Spacing between buttons */
                padding: 0.7rem;
                background-color: #1b1b1bc3;
                backdrop-filter: blur(5px);
                border-radius: 8.3px;
                /* Rounded corners */
                transition: 0.4s;
                z-index: 999;
            }

            .callToActionHover.hidden {
                opacity: 0;
                bottom: -1%;
                pointer-events: none;
            }

            .callToActionHover.visible {
                opacity: 1;
                bottom: 2%;
                pointer-events: all;
            }

            .callToActionHover .web {
                display: flex;
                gap: 1rem;
            }

            /* General button styling */
            .callToActionHover .btn {
                font-weight: 300;
                white-space: nowrap;
                flex: 1;
                color: white;
                padding: 0.7rem 1rem;
                border: 1px solid #4c4c4c;
                /* Border color */
                background-color: var(--ui-element-secondary-color);
                /* Dark background */
                font-size: 1.1rem;
                border-radius: 8.9px;
                cursor: pointer;
                transition: all 0.2s ease-in-out;
            }

            /* Button hover effect */
            .callToActionHover .btn:hover {
                background-color: #333;
                /* Slightly lighter background on hover */
                color: #ddd;
                /* Lighter text color on hover */
            }

            /* Specific styling for the "Buy" button */
            .callToActionHover .btn.buy {
                background-color: var(--secondary);
                /* Green background */
                color: #fff;
                /* White text */
                border: none;
                font-weight: 400;
            }

            .callToActionHover .mobile {
                display: none;
            }

            @media (max-width: 768px) {
                .callToActionHover {
                    width: 100%;
                    padding: 0.8rem;
                    border-radius: 3px;
                    bottom: 0;
                }

                .callToActionHover.visible {
                    opacity: 1;
                    bottom: -0.1%;
                    pointer-events: all;
                }

                .callToActionHover.hidden {
                    opacity: 1;
                    bottom: -0.1%;
                    pointer-events: all;
                }

                .callToActionHover .web {
                    display: none;
                }

                .callToActionHover .mobile {
                    display: flex;
                    gap: 1rem;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                }

                .callToActionHover .mobile .callback {
                    background-color: var(--ui-element-color);
                    border-radius: 3px;
                    padding: 1rem 0.5rem;
                    font-size: 1.4rem;
                    border: none;
                    width: 40%;
                }

                .callToActionHover .mobile .btn.buy span {
                    text-decoration: line-through;
                    font-size: 1.2rem;
                    opacity: 0.6;
                }

                .callToActionHover .mobile .btn.buy {
                    width: 60%;
                }

                .callToActionHover .btn {
                    width: 100%;
                    border-radius: 3px;
                    padding: 1rem 0.5rem;
                    font-size: 1.4rem;
                }
            }
        </style>
        <section>
            <div class="hidden callToActionHover">
                <div class="web">
                    <button onclick="window.location.href='/courses'" class="btn">Courses</button>
                    <!-- <button class="btn">Download Brochure</button> -->
                    <button onclick="openRequestCallbackSpecific('<%=course.course_name%>')" class="btn">Request
                        Callback</button>
                    <%if(status !="paid" ){%>
                        <button onclick="enroll()" class="btn buy">Register Now</button>
                        <%}else{%>
                            <button onclick="window.location.href='/classroom/gotoclassroom/<%=course._id%>'"
                                class="btn buy">Classroom</button>
                            <%}%>
                </div>
                <div class="mobile">
                    <button onclick="openRequestCallbackSpecific('<%=course.course_name%>')" class="callback">
                        <i class="ri-phone-line"></i>
                        Get Callback
                    </button>
                    <%if(status !="paid" ){%>
                        <button onclick="enroll()" class="btn buy">Register Now</span></button>
                </div>
                <%}else{%>
                    <button onclick="window.location.href='/classroom/gotoclassroom/<%=course._id%>'"
                        class="btn buy">Classroom</button>
                    <%}%>
            </div>
        </section>

        <script>
            window.addEventListener("scroll", () => {
                const scrollIndicator = document.querySelector(".callToActionHover");

                // Get the current scroll position
                const scrollPosition = window.scrollY;

                // Get the total scrollable height of the page
                const scrollHeight = document.documentElement.scrollHeight;
                const clientHeight = document.documentElement.clientHeight;

                // Set your scroll boundaries (adjust as needed)
                const showAt = 500; // Show after scrolling 200px
                const hideBeforeEnd = 700; // Hide 300px before the end of the page

                if (scrollPosition > showAt && scrollPosition < (scrollHeight - clientHeight - hideBeforeEnd)) {
                    scrollIndicator.classList.remove("hidden");
                    scrollIndicator.classList.add("visible");
                } else {
                    scrollIndicator.classList.remove("visible");
                    scrollIndicator.classList.add("hidden");
                }
            });
        </script>

        <%- include('../new-partials/footer-section.ejs') %>
            <%- include('../new-partials/footer.ejs') %>
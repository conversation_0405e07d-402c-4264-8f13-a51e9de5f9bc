</main>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/css/intlTelInput.css" />
<nav>
  <%if(request && !request.user.isVerified){%>
    <div class="phoneNumberPopup">
      <div class="forms">
        <div id="basic" class="details">
          <div class="inputfield">
            <h1>Secure your account</h1>
            <p for="ContactNumber">Please enter your phone number to verify your account.</p>
            <div class="container">
              <input id="phoneNumber" type="text" name="phoneNumber" placeholder="Enter Your Mobile Number"
                maxlength="10" inputmode="numeric" pattern="[0-9]*"
                oninput="this.value = this.value.replace(/[^0-9]/g, '');" />
            </div>
            <p> <br> (Not from India? - Contact support for manual verification: <br> <a href="tel:+************">+91
                **********</a> or <a href="mailto:<EMAIL>"> <EMAIL></a>)</p>

            <small id="contactNumberValidation"></small>
          </div>
          <input type="hidden" id="recaptchaToken" name="recaptchaToken">
        </div>
        <div id="otp-verification" class="details">
          <div class="loader-container">
            <div class="loader">
              <div class="progress" id="progress"></div>
            </div>
          </div>
          <h2>OTP Verification</h2>
          <div class="inputfield">
            <label style="text-align: center;" for="otp">Enter the OTP sent to +91 - <span
                id="otp-mobile-number"></span> <span style="text-decoration: underline;" id="edit-mobile-number"
                onclick="document.getElementById('phoneNumber').focus()">
                <i class="ri-pencil-line"></i>
                edit
              </span>
            </label>
            <div class="otp-container" id="otp-container">
              <input autocomplete="off" type="text" maxlength="1" class="otp-input" id="digit1">
              <input autocomplete="off" type="text" maxlength="1" class="otp-input" id="digit2">
              <input autocomplete="off" type="text" maxlength="1" class="otp-input" id="digit3">
              <input autocomplete="off" type="text" maxlength="1" class="otp-input" id="digit4">
            </div>
            <p id="otpErrorMessage"> </p>
            <p id="resend-otp"><span id="resend-otp-text">Resend OTP in</span> <span class="timer">0:30</span></p>
          </div>
        </div>
      </div>
    </div>
    <%}%>
      <div class="header">

        <div class="left" style="gap: 0.5rem;" onclick="window.location.href='/'">
          <img src="https://ik.imagekit.io/sheryians/light-logo_lNzGXRRlQ.png" alt="">
          <!-- <div style="height:1.8em; width:1px; background-color:white; display: inline-block; vertical-align: middle;"></div> -->
          <span style="margin-top: 0.2em;">Sheryians <br> coding school</span>
        </div>
        <div class="right">
          <a href="/" class="nav-item">
            <span>Home</span>
          </a>
          <a href="/courses" class="nav-item">
            <span>Courses</span>
          </a>
          <a href="/kodr" class="nav-item">
            <span class="" style="font-weight: 600; font-size: 1.2rem;">Kodr 3.0</span>
          </a>
          <a href="/Kodex" class="nav-item">
            <span class="" style="font-weight: 600; font-size: 1.2rem;">Kodex</span>
          </a>

          <button onclick="openRequestCallback()" class="nav-item mobile-item request-callback">
            <i class="ri-phone-line"></i>
            <span>
              Request Callback
            </span>
          </button>

          <% if(request){ %>
            <a href="/classroom" class="nav-item">
              <span>Classroom</span>
            </a>

            <button class="nav-item mobile-item notification-icon">
              <span class="notification-toggle">
                <svg onclick="renderTimeSinceDate()" enable-background="new 0 0 24 24" height="24" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path fill="currentColor" class="ol1"
                    d="M10 20h4c0 1.1-.9 2-2 2s-2-.9-2-2zm10-2.65V19H4v-1.65l2-1.88v-5.15C6 7.4 7.56 5.1 10 4.34v-.38c0-1.42 1.49-2.5 2.99-1.76.65.32 1.01 1.03 1.01 1.76v.39c2.44.75 4 3.06 4 5.98v5.15l2 1.87z">
                  </path>
                </svg>

                <i class="ri-close-line"></i>

              </span>
              <div class="notification-panel">
                <div class="notification-content">
                  <div class="heading">
                    <p>Notification</p>
                  </div>
                  <div class="notifications">
                    <%request && request.user.notifications.reverse().forEach((noti)=>{%>
                      <div class="notification <%=noti.seen ? "" : " new" %>"
                        onclick="openNotification('<%=noti.link%>','<%=noti._id%>')"
                            >
                            <div class="left">
                              <div class="notification-heading">
                                <p>
                                  <%=noti.message%>
                                </p>
                                <small data-time="<%=noti.createdAt%>"></small>
                              </div>
                            </div>
                            <div class="right">
                              <img src="<%=noti.thumbnail_url%>" alt="">
                            </div>
                      </div>
                      <%})%>
                  </div>
                </div>

              </div>
              <div class="remove-notification-panel">
              </div>
            </button>
            <% } %>



              <button class="nav-item  auth-user ">

                <% if(request){ %>
                  <a href="/myAccount">
                    <div onclick="/myAccount" class="text-profile-initial btn btn-primary">
                      <img onerror="handleImageError()" src="<%= request.user.avatar %>" alt="">
                    </div>
                  </a>
                  <% }else{ %>
                    <a href="/signin" class="btn btn-primary sign-in">
                      Sign In
                    </a>
                    <% } %>
              </button>

              <button class="menu nav-item">
                <div class="menu-toggle">
                  <div class="line line1"></div>
                  <div class="line line2"></div>
                </div>

                <div class="menu-panel">
                  <div class="menu-header">
                    <h1>Menu</h1>
                  </div>
                  <div class="menu-items">
                    <a href="/" class="nav-item menu-item mobile-item">Home</a>
                    <a href="/courses" class="nav-item menu-item mobile-item">Courses</a>
                    <a href="/kodr" class="nav-item menu-item mobile-item " style=" font-weight: 600;">Kodr 3.0</a>
                    <a href="/kodex" class="nav-item menu-item mobile-item " style=" font-weight: 600;">Kodex</a>
                    <!-- <a href="https://reimagine.sheryians.com" class="nav-item menu-item mobile-item">Re-Imagine</a> -->
                    <% if(request && request.user){ %>
                      <a href="/classroom" class="nav-item menu-item mobile-item">Classroom</a>
                      <% } %>
                        <% if(request && request.user){ %>
                          <a href="/myaccount" class="nav-item menu-item mobile-item">Profile</a>
                          <% } else { %>
                            <a href="/signin" class="nav-item menu-item mobile-item">Sign In</a>

                            <% } %>
                  </div>
                </div>

              </button>

        </div>




        <script defer>
          let lastTime = 0;
          let maxFPS = 60; // Change this value to set the maximum FPS
          let direction = 1

          const nav = document.querySelector('nav');
          let lastScrollY = window.scrollY;
          let navRect = null;

          function navPosition(timestamp) {
            if (!lastTime) {
              lastTime = timestamp;
            }
            const elapsed = timestamp - lastTime;
            if (elapsed > (1000 / maxFPS)) {
              lastTime = timestamp;



              if (navRect) {
                if (direction === 1 && window.scrollY > 0) {
                  nav.style.transform = `translateY(-${navRect.height}px)`;
                } else {
                  if (document.querySelector(".holyoffer")) {
                    nav.style.transform = `translateY(40px)`;
                  } else {
                    nav.style.transform = `translateY(0px)`;
                  }
                }
              } else {
                navRect = nav.getBoundingClientRect();
              }

              if (lastScrollY > window.scrollY) {
                direction = -1;
              }

              if (lastScrollY < window.scrollY) {
                direction = 1;
              }
              lastScrollY = window.scrollY;



            }
            requestAnimationFrame(navPosition);
          }
          requestAnimationFrame(navPosition);


          if (document.querySelector('.notification-toggle')) {
            document.querySelector('.notification-toggle').addEventListener('click', () => {
              document.querySelector('.notification-panel').classList.toggle('show');
            });

            document.querySelector('.remove-notification-panel').addEventListener('click', () => {
              document.querySelector('.notification-panel').classList.remove('show');
            });
          }


          document.querySelector('.menu-toggle').addEventListener('click', function () {
            if (this.classList.contains('menu-show')) {
              this.classList.add('menu-close')
            } else {
              this.classList.remove('menu-close')
            }
            this.classList.toggle('menu-show')

          })

          function handleImageError(e) {
            document.querySelector(".text-profile-initial").innerHTML = "A"
            document.querySelector(".text-profile-initial").style.backgroundColor = "var(--accent-color)"
            document.querySelector(".text-profile-initial").style.display = "flex"
            document.querySelector(".text-profile-initial").style.justifyContent = "center"
            document.querySelector(".text-profile-initial").style.alignItems = "center"
          }
        </script>
      </div>

      <section class="popup request-callback">
        <div class="bg" onclick="closePopup()"></div>
        <div class="center">
          <div class="text">
            <h3>Request a callback</h3>
            <small>Fill the form below to request a callback from our team.</small>
            <form action="/request-callback" method="POST" onsubmit="requestCallback(event)">
              <div class="form-group">
                <label for="name">Name</label>
                <input type="text" name="name" id="name" placeholder="Name" required>
                <span class="error">Invalid Name</span>
              </div>
              <div class="form-group">
                <label for="phone">Phone</label>
                <input type="tel" name="contact" id="phone" placeholder="Phone" required>
                <span class="error">Invalid Phone</span>
              </div>
              <div class="form-group">
                <label for="datetime-request-callback">When should we call you?
                  <i class="ri-calendar-line"></i>
                </label>
                <input type="datetime-local" name="datetime" id="datetime-request-callback" required
                  value="<%= new Date(Date.now() + 86400000).toISOString().slice(0, 10) + 'T13:30' %>">
                <span class="error">Invalid Date and Time</span>
              </div>
              <div class="form-group">
                <label for="datetime-request-callback">Enquiry For
                  <i class="ri-calendar-line"></i>
                </label>
                <select name="enquiryFor">
                  <option selected value="online">Online Courses (Website)</option>
                  <option value="offline">Offline Batches (Bhopal)</option>
                </select>
                <span class="error">Invalid Date and Time</span>
              </div>
              <div class="form-group courseName">
                <label for="datetime-request-callback">Which course
                  <i class="ri-calendar-line"></i>
                </label>
                <select name="courseName">
                  <option selected value="kodr">KODR</option>
                  <option value="web-development">Web Development</option>
                  <option value="dsa">DSA</option>
                  <option value="data-science">Python & Data Science</option>
                  <option value="java">Java</option>
                  <option value="c-programming">C Programming</option>
                  <option value="android">Android</option>
                  <option value="others">Others</option>
                </select>
                <span class="error">Invalid Date and Time</span>
              </div>
              <input style="display: none;" type="text" name="courseName" value="">
              <div class="form-group submit">
                <button type="submit" class="btn btn-primary"
                  style="font-size:1.3rem; width: 100%; background-color: var(--accent-color); color: black;">Submit</button>
              </div>
            </form>
          </div>
          <div onclick="closePopup()" class="closePopup">
            <i class="ri-close-circle-line"></i>
          </div>
        </div>
      </section>
      <section onclick="closePopup()" class="popup discordJoinPopup">
        <div class="center">
          <div class="images">
            <img src="/Sheryians_Logo_wFKd9VClG.png" alt="">
            <i class="ri-add-line"></i>
            <img src="/images/discord/discord.png" alt="">
          </div>
          <div class="text">
            <h3>
              Authorize discord to access course support
            </h3>
            <small> in order to access our course support channel on the discord you have to authorize us from the
              discord.
            </small>

            <div class="buttons">
              <button class="cancel">Cancel</button>
              <button onclick="window.open('/classroom/joinChannel/channle/role')" class="authorize">Authorize</button>
            </div>

          </div>
          <div onclick="closeDiscordPopup()" class="closePopup">
            <i class="ri-close-circle-line"></i>
          </div>
        </div>
      </section>
</nav>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.4.0/axios.min.js"
  integrity="sha512-uMtXmF28A2Ab/JJO2t/vYhlaa/3ahUOgj1Zf27M5rOo8/+fcTUVH0/E0ll68njmjrLqOBjXM3V9NiPFL5ywWPQ=="
  crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script>
<script>
  // const darkIcon = 'https://ik.imagekit.io/sheryians/light-logo_lNzGXRRlQ.png';
  // const lightIcon = 'https://ik.imagekit.io/sheryians/dark-logo_TwETv_UJBX.png';

  // const updateFavicons = () => {
  //   const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  //   const newHref = isDark ? darkIcon : lightIcon;
  //   console.log(newHref);
  //   // Get all link tags with rel=icon or rel=apple-touch-icon
  //   document.querySelectorAll('link[rel="icon"], link[rel="apple-touch-icon"]').forEach(link => {
  //     link.href = newHref;
  //   });
  // };

  // // Initial call
  // updateFavicons();

  // // Listen for theme changes
  // window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', updateFavicons);
</script>
<script>
  // Initialize the intl-tel-input for the phone input
  const phnInp = document.getElementById("phone");
  const iti = intlTelInput(phnInp, {
    initialCountry: "IN",
    utilsScript:
      "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js", // For number formatting
  });

  // Add validation before form submission
</script>

<script defer>



  function lazyLoadImages(attribute) {
    const lazyImages = document.querySelectorAll(`[${attribute}]`);

    const options = {
      root: null, // viewport
      rootMargin: '0px',
      threshold: 0.1 // load when 10% of the image is visible
    };

    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.lazySrc;
          img.removeAttribute('data-lazy-src');
          observer.unobserve(img);
        }
      });
    }, options);

    lazyImages.forEach(image => {
      imageObserver.observe(image);
    });
  }
  async function openNotification(link, id) {
    try {
      axios.post("/classroom/notifications/update", {
        notificationId: id
      })
      window.location.href = link
    } catch (err) {
      console.log(err)
    }
  }

  function renderTimeSinceDate() {
    document.querySelectorAll(".notifications small").forEach((elem) => {
      elem.textContent = timeSince(new Date(elem.getAttribute('data-time')))
    })
  }

  function timeSince(date) {
    // Get the current date
    const now = new Date();

    // Calculate the difference in milliseconds
    const difference = now.getTime() - date.getTime();

    // Handle negative difference (future date)
    if (difference < 0) {
      return "Date in the future";
    }

    // Calculate days and months
    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const months = Math.floor(days / 30);

    // Determine unit (days or months)
    const unit = months >= 1 ? "months" : "days";
    const value = months >= 1 ? months : days;

    // Format the output string
    const output = `${value} ${unit} ago`;

    return output;
  }


  async function joinDiscord(channel_id, role_id, user_id, question) {

    document.querySelector(".askDoubtBtn").style.opacity = "0.5"
    document.querySelector(".askDoubtBtn").style.pointerEvents = "none"
    let response = await axios.post(`/classroom/is_user_in_guild/${user_id}`)
    const isUserInGuild = response.data.is_user_in_guild

    if (!isUserInGuild) {
      openDiscordPopup()
      return
    }

    const currentLectureId = document.querySelector('.currentPlaying').getAttribute('data-lecture-id')
    try {
      const res = await axios.post(`/classroom/ticket/${currentLectureId}`, { question })
      console.log(res)
      document.querySelector(".askDoubtBtn").style.opacity = "1"
      document.querySelector(".askDoubtBtn").style.pointerEvents = "all"
      window.open(res.data.url)
    } catch (err) {
      document.querySelector(".askDoubtBtn").style.opacity = "1"
      document.querySelector(".askDoubtBtn").style.pointerEvents = "all"
      console.log(err)
      document.querySelector('.discordJoinPopup').remove()
    }
  }

  async function enterDiscord(channel_id, role_id, user_id) {
    let response = await axios.post(`/classroom/is_user_in_guild/${user_id}`)
    const isUserInGuild = response.data.is_user_in_guild

    if (!isUserInGuild) {
      openDiscordPopup()
      return
    }

    window.open(`/classroom/joinChannel/${channel_id}/${role_id}`)
  }

  function openDiscordPopup() {
    document.querySelector('.discordJoinPopup').style.display = 'flex'
    document.querySelector('.discordJoinPopup').classList.add('active')
    document.querySelector('.discordJoinPopup').style.opacity = '1'
    try {
      if (lenis)
        lenis.stop()
    } catch (err) {

    }
  }



  function closePopup() {
    document.querySelectorAll('.popup').forEach(pop => {
      pop.classList.remove('active')
      pop.style.display = 'none'
      pop.style.opacity = '0'
    })

    try {
      if (lenis)
        lenis.start()
    } catch (er) {

    }
  }

  function openRequestCallback() {
    const requestCallback = document.querySelector('.popup.request-callback')
    requestCallback.style.display = 'flex'
    requestCallback.classList.add('active')
    requestCallback.style.opacity = '1'
    console.log("object");
  }

  function openRequestCallbackSpecific(courseName) {
    console.log(courseName);
    const requestCallback = document.querySelector('.popup.request-callback')
    document.querySelector(".courseName").setAttribute("courseName", courseName)
    requestCallback.querySelector(".courseName").value = courseName
    requestCallback.style.display = 'flex'
    requestCallback.classList.add('active')
    requestCallback.style.opacity = '1'
  }

  function requestCallback(event) {
    event.preventDefault()

    console.log(event.target)

    const nameInput = event.target.querySelector('#name');
    const phoneInput = event.target.querySelector('#phone');
    const datetimeInput = event.target.querySelector('#datetime-request-callback');

    let isValid = true;

    if (!nameInput.value.trim()) {
      nameInput.classList.add('error');
      isValid = false;
    } else {
      nameInput.classList.remove('error');
    }

    if (!iti.isValidNumber()) {
      phoneInput.classList.add('error');
      isValid = false;
    } else {
      phoneInput.classList.remove('error');
    }

    if (!datetimeInput.value.trim()) {
      datetimeInput.classList.add('error');
      isValid = false;
    } else {
      datetimeInput.classList.remove('error');
    }

    if (!isValid) {
      return;
    }

    const form = event.target
    const data = new FormData(form)
    console.log(data.get('enquiryFor'));
    <%if (typeof course !== "undefined") {%>
      if (data.get('enquiryFor') == "online") {
        data.set("courseName", "<%=course.course_name%>")
      }
    <%} else {%>
      if (data.get('enquiryFor') == "online") {
        data.set("courseName", "NA")
      }
    <%}%>
      console.log(iti.getNumber())
    axios.post(form.action, {
      name: data.get('name'),
      contact: iti.getNumber(),
      datetime: data.get('datetime'),
      enquiryFor: data.get('enquiryFor'),
      courseName: data.get('courseName')
    }).then((response) => {
      console.log(response)
      closePopup()
    }).catch((err) => {
      console.log(err)
    })
  }



  lazyLoadImages('data-lazy-src')
  lazyLoadImages('srcset')

  // Show/hide courseName input based on enquiryFor selection
  function toggleCourseNameField() {
    const courseNameContainer = document.querySelector('.courseName');
    const enquirySelect = document.querySelector('select[name="enquiryFor"]');

    if (enquirySelect && courseNameContainer) {
      if (enquirySelect.value === 'offline') {
        courseNameContainer.style.display = 'flex';
      } else {
        courseNameContainer.style.display = 'none';
        <%if (typeof course !== "undefined") {%>
          console.log("<%=course.course_name%>");
          courseNameContainer.querySelector('select[name="courseName"]').value = "<%=course.course_name%>";
        <%}%>
      }
    }
  }

  // Add event listener to enquiry select dropdown
  document.addEventListener('DOMContentLoaded', function () {
    const enquirySelect = document.querySelector('select[name="enquiryFor"]');
    if (enquirySelect) {
      // Set initial state
      toggleCourseNameField();

      // Add event listener for changes
      enquirySelect.addEventListener('change', toggleCourseNameField);
    }
  });
</script>

<script>
  const phoneNumber = document.getElementById('phoneNumber');
  const contactNumberValidation = document.getElementById('contactNumberValidation');
  const otpErrorMessage = document.getElementById('otpErrorMessage');
  const email = '<%=request ? request.user.email : ""%>'
  const indianMobileNumberRegex = /^(\+91|91)[6-9]\d{9}$/;

  const sendOTP = async (phoneNumber) => {
    const response = await fetch('/signup/send-otp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phoneNumber }),
    });
    const data = await response.json();
    return data;
  };

  let interval; // Declare interval in the outer scope

  // Function to start the timer for OTP resend
  const startTimer = () => {
    stopTimer();
    let time = 30;
    const timer = document.querySelector('.timer');
    const resendOTP = document.getElementById('resend-otp');
    const resendOTPText = document.getElementById('resend-otp-text');
    resendOTP.style.pointerEvents = 'none';
    resendOTP.style.cursor = 'not-allowed';
    resendOTPText.textContent = 'Resend OTP in';
    interval = setInterval(() => {
      time -= 1;
      timer.textContent = `0:${time < 10 ? `0${time}` : time}`;
      if (time === 0) {
        clearInterval(interval);
        resendOTP.style.cursor = 'pointer';
        resendOTP.style.pointerEvents = 'auto';
        timer.textContent = '';
        resendOTPText.textContent = 'Resend OTP';
      }
    }, 1000);
  };

  // Function to stop the timer for OTP resend and clear the timer function
  const stopTimer = () => {
    const timer = document.querySelector('.timer');
    timer.textContent = '';
    clearInterval(interval); // Clear the interval
  };

  document.getElementById('resend-otp').addEventListener('click', async () => {
    const timer = document.querySelector('.timer');
    const phoneNumber = '+91' + document.getElementById('phoneNumber').value;
    if (timer.textContent === '') {
      const response = await fetch('/signup/resend-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber }),
      });
      const data = await response.json();
      if (!data.message == 'success') {
        document.getElementById('otpErrorMessage').textContent = data.reason || 'Error while sending OTP';
      }
      // clear the OTP input fields
      document.querySelectorAll('.otp-input').forEach((input) => {
        input.value = '';
      });
      document.getElementById('digit1').focus();
      resetOTPInput();
      document.getElementById('otpErrorMessage').textContent = '';
      startTimer();
    }
  });

  function disableOTPInputsAndStartTimer() {
    const otpInputs = document.querySelectorAll('.otp-input');
    otpInputs.forEach((input) => {
      input.disabled = true;
      input.opacity = 0.5;
    });
    // startTimer();
  }

  function validateOTP(otp) {
    updateLoader()
    const phoneNumber = '+91' + document.getElementById('phoneNumber').value;
    disableOTPInputsAndStartTimer();
    fetch('/signup/verify-account', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phoneNumber, otp, email }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.message === 'success') {
          // Proceed to the next step
          setOTPInput("success")
          document.querySelector(".phoneNumberPopup").remove()
          window.location.href = data.redirectTo;
        } else {
          setOTPInput("error")
          otpErrorMessage.textContent = data.reason || 'Invalid OTP';
        }
      })
      .catch((error) => {
        console.error('Error verifying OTP:', error);
        otpErrorMessage.textContent = 'An error occurred. Please try again.';
      }).finally(() => {
        resetLoader()
      });
  }



  // OTP input fields auto focus and backspace, paste functionality
  const otpInputs = document.querySelectorAll('.otp-input');

  otpInputs.forEach((input, index) => {
    document.querySelectorAll(".otp-input").forEach((inp) => {
      inp.style.border = "1px solid transparent";
    })
    input.addEventListener('input', (e) => {
      // Allow only digits
      input.value = input.value.replace(/[^0-9]/g, '');

      // Move to next input if a digit is entered
      if (input.value.length === 1 && index < otpInputs.length - 1) {
        otpInputs[index + 1].focus();
      }

      // Collect the OTP from all inputs
      const otp = Array.from(otpInputs).map((input) => input.value).join('');

      // If all digits are entered, validate the OTP
      if (otp.length === otpInputs.length) {
        validateOTP(otp);
      }
    });

    // Handle backspace to move focus to the previous input
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Backspace' && input.value === '' && index > 0) {
        otpInputs[index - 1].focus();
      }
    });

    // Handle paste event
    input.addEventListener('paste', (e) => {
      e.preventDefault();
      const pasteData = e.clipboardData.getData('text').replace(/\D/g, '');
      const digits = pasteData.split('');
      otpInputs.forEach((input, idx) => {
        input.value = digits[idx] || '';
      });

      // Focus the next input or the last one
      const nextInputIndex = digits.length < otpInputs.length ? digits.length : otpInputs.length - 1;
      otpInputs[nextInputIndex].focus();

      // Collect the OTP from all inputs
      const otp = Array.from(otpInputs).map((input) => input.value).join('');

      // If all digits are entered, validate the OTP
      if (otp.length === otpInputs.length) {
        validateOTP(otp);
      }
    });
  });

  // check email validation while typing

  // Debounce function to limit the rate of function calls
  function debounce(func, delay) {
    let debounceTimer;
    return function (...args) {
      const context = this;
      clearTimeout(debounceTimer);
      debounceTimer = setTimeout(() => func.apply(context, args), delay);
    };
  }

  // Event listener for email input


  const phoneNumberInput = document.getElementById('phoneNumber');
  // Event listener for phone number input
  phoneNumberInput.addEventListener('input', debounce(async function (e) {
    const value = e.target.value.trim();

    if (value.length != 10) {
      return;
    }

    const formattedNumber = `+91${value}`;

    // Validate phone number format (10 digits)
    if (indianMobileNumberRegex.test(formattedNumber)) {
      contactNumberValidation.textContent = 'Checking phone number availability...';
      contactNumberValidation.style.color = 'white';

      try {
        const response = await fetch('/signup/check-phone-number', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ phoneNumber: formattedNumber }),
        });

        const data = await response.json();

        if (data.message !== 'success') {
          contactNumberValidation.textContent = data.reason || 'Error checking phone number';
          contactNumberValidation.style.color = 'var(--error-color)';
        } else {
          contactNumberValidation.textContent = '';
          setTimeout(() => {
            document.querySelector('#otp-verification').scrollIntoView({ behavior: 'smooth' });
          }, 0);
          // clear the OTP input fields
          document.querySelectorAll('.otp-input').forEach((input) => {
            input.value = '';
          });
          document.querySelector('.otp-input').focus();
          resetOTPInput();
          document.getElementById('otpErrorMessage').textContent = '';

          contactNumberValidation.textContent = '';
          document.getElementById('otp-mobile-number').textContent = value;
          startTimer();
          sendOTP(formattedNumber).then((data) => {
            if (!data.message == 'success') {
              contactNumberValidation.textContent = data.reason || 'Error while sending OTP';
            }
          });
        }
      } catch (error) {
        console.error('Error checking phone number:', error);
        contactNumberValidation.textContent = 'Error checking phone number';
        contactNumberValidation.style.color = 'var(--error-color)';
      }
    } else {
      contactNumberValidation.textContent = 'Invalid phone number';
      contactNumberValidation.style.color = 'var(--error-color)';
    }
  }, 500)); // Adjust the delay as needed (e.g., 500 milliseconds)

  function updateLoader() {
    document.querySelectorAll(".otp-input").forEach((input) => {
      input.disabled = true;
      input.style.cursor = "not-allowed";
      input.style.opacity = "0.5";
    });
  }

  function resetLoader() {
    document.querySelectorAll(".otp-input").forEach((input) => {
      input.disabled = false;
      input.style.cursor = "text";
      input.style.opacity = "1";
    });
  }

  function setOTPInput(type) {
    if (type == "error") {
      document.querySelectorAll(".otp-input").forEach((input) => {
        input.style.border = "1px solid var(--error-color)";
      });
    } else {
      document.querySelectorAll(".otp-input").forEach((input) => {
        input.style.border = "1px solid green";
      });
    }
  }

  function resetOTPInput() {
    document.querySelectorAll(".otp-input").forEach((input) => {
      input.style.border = "1px solid transparent";
    });
  }

  function copyToClipboard(coupon) {
    let text = document.querySelector(".clicktocopy")
    let textDiv = document.querySelector(".copy")
    textDiv.style.backgroundColor = "#8ED968"
    textDiv.style.setProperty('--arrow-color', '#8ED968')
    setTimeout(() => {
      text.textContent = "Copied";
    }, 25);
    setTimeout(() => {
      text.textContent = "Click to copy";
      textDiv.style.setProperty('--arrow-color', 'black')
      textDiv.style.backgroundColor = "black"
    }, 2000);
    navigator.clipboard.writeText(coupon)
  }
</script>

<script>

  let chatLoading = false;
  let IframeLectureId = null;
  async function openChat() {

    const currentLectureId = document.querySelector('.currentPlaying[data-type=video]')?.getAttribute('data-lecture-id')


    try {


      const chatScreen = document.querySelector('.chat-screen');

      if (!chatScreen) {
        console.log("No chat screen found")
        return
      }

      chatScreen.classList.remove("loaded")


      chatScreen.style.display = 'flex';

      if (chatScreen.querySelector("iframe") && IframeLectureId == currentLectureId) {
        chatScreen.classList.add("loaded")
        return
      }
      if (chatLoading && IframeLectureId == currentLectureId) {
        chatScreen.classList.add("loaded")
        console.log("Chat is already loading");
        return;
      }

      chatLoading = true;
      const response = await axios.get(`/classroom/get-chat-token/${currentLectureId}`);

      const data = response.data

      if (!data.iframeUrl) {
        console.log("No iframe URL found")
        chatScreen.style.display = 'none';
        return
      }



      chatScreen.classList.add("loaded")
      if (chatScreen.querySelector("iframe")) {
        chatScreen.querySelector("iframe").remove()
      }
      const iframe = document.createElement('iframe');
      iframe.src = data.iframeUrl;
      iframe.allow = "camera; microphone; fullscreen; display-capture; clipboard-read; clipboard-write";
      chatScreen.appendChild(iframe);

      IframeLectureId = currentLectureId;

    } catch (err) {
      console.log(err)
    }

  }



  async function closeChat() {
    const chatScreen = document.querySelector('.chat-screen');
    if (chatScreen) {
      chatScreen.style.display = 'none';
    }
  }


  async function openChatOnNewPage() {
    const currentLectureId = document.querySelector('.currentPlaying[data-type=video]')?.getAttribute('data-lecture-id')
    if (!currentLectureId) {
      console.log("No current video lecture found")
      return
    }

    window.open(`/classroom/chat/${currentLectureId}`, '_blank')
  }


</script>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N9CBSHXN" height="0" width="0"
    style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
</body>

</html>